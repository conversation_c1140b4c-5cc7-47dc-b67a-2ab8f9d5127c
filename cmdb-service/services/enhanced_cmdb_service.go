package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedCMDBService 增强的CMDB服务接口
type EnhancedCMDBService interface {
	// 基础CRUD操作
	CMDBService

	// 批量操作
	BatchCreateDevLanguages(ctx context.Context, languages []*models.DevLanguageCreateRequest) ([]*models.DevLanguage, error)
	BatchUpdateDevLanguages(ctx context.Context, updates map[uint]*models.DevLanguageUpdateRequest) error
	BatchDeleteDevLanguages(ctx context.Context, ids []uint) error

	BatchCreateProducts(ctx context.Context, products []*models.ProductCreateRequest) ([]*models.Product, error)
	BatchUpdateProducts(ctx context.Context, updates map[uint]*models.ProductUpdateRequest) error
	BatchDeleteProducts(ctx context.Context, ids []uint) error

	// 缓存操作
	ClearCache(resourceType string) error
	RefreshCache(resourceType string) error

	// 统计分析
	GetResourceStats(ctx context.Context) (*models.ResourceStats, error)
	GetUsageAnalytics(ctx context.Context, req *models.AnalyticsRequest) (*models.AnalyticsResponse, error)

	// 导入导出
	ExportResources(ctx context.Context, req *models.ExportRequest) ([]byte, error)
	ImportResources(ctx context.Context, req *models.ImportRequest) (*models.ImportResult, error)

	// 关系管理
	GetResourceDependencies(ctx context.Context, resourceType string, resourceID uint) (*models.DependencyGraph, error)
	ValidateResourceDeletion(ctx context.Context, resourceType string, resourceID uint) (*models.ValidationResult, error)
}

// enhancedCMDBService 增强的CMDB服务实现
type enhancedCMDBService struct {
	CMDBService
	db     *gorm.DB
	logger *logrus.Logger
	cache  *ResourceCache
	mutex  sync.RWMutex
}

// ResourceCache 资源缓存
type ResourceCache struct {
	data       map[string]interface{}
	expiration map[string]time.Time
	mutex      sync.RWMutex
	ttl        time.Duration
}

// NewResourceCache 创建资源缓存
func NewResourceCache(ttl time.Duration) *ResourceCache {
	cache := &ResourceCache{
		data:       make(map[string]interface{}),
		expiration: make(map[string]time.Time),
		ttl:        ttl,
	}

	// 启动清理goroutine
	go cache.cleanup()

	return cache
}

// Get 获取缓存数据
func (c *ResourceCache) Get(key string) (interface{}, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if exp, exists := c.expiration[key]; exists && time.Now().Before(exp) {
		if data, exists := c.data[key]; exists {
			return data, true
		}
	}

	return nil, false
}

// Set 设置缓存数据
func (c *ResourceCache) Set(key string, value interface{}) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.data[key] = value
	c.expiration[key] = time.Now().Add(c.ttl)
}

// Delete 删除缓存数据
func (c *ResourceCache) Delete(key string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	delete(c.data, key)
	delete(c.expiration, key)
}

// Clear 清空缓存
func (c *ResourceCache) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.data = make(map[string]interface{})
	c.expiration = make(map[string]time.Time)
}

// cleanup 清理过期缓存
func (c *ResourceCache) cleanup() {
	ticker := time.NewTicker(time.Minute * 5)
	defer ticker.Stop()

	for range ticker.C {
		c.mutex.Lock()
		now := time.Now()
		for key, exp := range c.expiration {
			if now.After(exp) {
				delete(c.data, key)
				delete(c.expiration, key)
			}
		}
		c.mutex.Unlock()
	}
}

// NewEnhancedCMDBService 创建增强的CMDB服务
func NewEnhancedCMDBService(db *gorm.DB, logger *logrus.Logger) EnhancedCMDBService {
	baseCMDBService := NewCMDBService(db, logger)
	cache := NewResourceCache(time.Minute * 15) // 15分钟缓存TTL

	return &enhancedCMDBService{
		CMDBService: baseCMDBService,
		db:          db,
		logger:      logger,
		cache:       cache,
	}
}

// =====批量操作实现=====

// BatchCreateDevLanguages 批量创建开发语言
func (s *enhancedCMDBService) BatchCreateDevLanguages(ctx context.Context, languages []*models.DevLanguageCreateRequest) ([]*models.DevLanguage, error) {
	if len(languages) == 0 {
		return nil, fmt.Errorf("no languages to create")
	}

	s.logger.WithField("count", len(languages)).Info("批量创建开发语言")

	var results []*models.DevLanguage

	err := s.db.Transaction(func(tx *gorm.DB) error {
		for _, req := range languages {
			// 检查语言代码是否已存在
			var existing models.DevLanguage
			if err := tx.Where("language_code = ?", req.LanguageCode).First(&existing).Error; err == nil {
				return fmt.Errorf("language code %s already exists", req.LanguageCode)
			}

			language := &models.DevLanguage{
				LanguageCode:   req.LanguageCode,
				Name:           req.Name,
				BaseImage:      req.BaseImage,
				BuildSettings:  req.BuildSettings,
				DockerTemplate: req.DockerTemplate,
				K8sTemplate:    req.K8sTemplate,
				Labels:         req.Labels,
				Description:    req.Description,
				SortOrder:      req.SortOrder,
			}

			if err := tx.Create(language).Error; err != nil {
				return fmt.Errorf("failed to create language %s: %w", req.LanguageCode, err)
			}

			results = append(results, language)
		}

		return nil
	})

	if err != nil {
		s.logger.WithError(err).Error("批量创建开发语言失败")
		return nil, err
	}

	// 清除相关缓存
	s.cache.Delete("dev_languages")

	s.logger.WithField("created_count", len(results)).Info("批量创建开发语言成功")
	return results, nil
}

// BatchUpdateDevLanguages 批量更新开发语言
func (s *enhancedCMDBService) BatchUpdateDevLanguages(ctx context.Context, updates map[uint]*models.DevLanguageUpdateRequest) error {
	if len(updates) == 0 {
		return fmt.Errorf("no updates provided")
	}

	s.logger.WithField("count", len(updates)).Info("批量更新开发语言")

	err := s.db.Transaction(func(tx *gorm.DB) error {
		for id, req := range updates {
			updateData := make(map[string]interface{})

			if req.Name != nil {
				updateData["name"] = *req.Name
			}
			if req.BaseImage != nil {
				updateData["base_image"] = *req.BaseImage
			}
			if req.BuildSettings != nil {
				updateData["build_settings"] = *req.BuildSettings
			}
			if req.DockerTemplate != nil {
				updateData["docker_template"] = *req.DockerTemplate
			}
			if req.K8sTemplate != nil {
				updateData["k8s_template"] = *req.K8sTemplate
			}
			if req.Labels != nil {
				updateData["labels"] = *req.Labels
			}
			if req.Description != nil {
				updateData["description"] = *req.Description
			}
			if req.SortOrder != nil {
				updateData["sort_order"] = *req.SortOrder
			}

			if len(updateData) > 0 {
				updateData["updated_at"] = time.Now()
				if err := tx.Model(&models.DevLanguage{}).Where("id = ?", id).Updates(updateData).Error; err != nil {
					return fmt.Errorf("failed to update language %d: %w", id, err)
				}
			}
		}

		return nil
	})

	if err != nil {
		s.logger.WithError(err).Error("批量更新开发语言失败")
		return err
	}

	// 清除相关缓存
	s.cache.Delete("dev_languages")

	s.logger.Info("批量更新开发语言成功")
	return nil
}

// BatchDeleteDevLanguages 批量删除开发语言
func (s *enhancedCMDBService) BatchDeleteDevLanguages(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return fmt.Errorf("no IDs provided")
	}

	s.logger.WithField("ids", ids).Info("批量删除开发语言")

	// 检查是否有关联的微应用
	var count int64
	if err := s.db.Model(&models.MicroApp{}).Where("language_code IN (SELECT language_code FROM cmdb_dev_languages WHERE id IN ?)", ids).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to check language dependencies: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("cannot delete languages: %d micro apps are using these languages", count)
	}

	err := s.db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("id IN ?", ids).Delete(&models.DevLanguage{}).Error; err != nil {
			return fmt.Errorf("failed to delete languages: %w", err)
		}
		return nil
	})

	if err != nil {
		s.logger.WithError(err).Error("批量删除开发语言失败")
		return err
	}

	// 清除相关缓存
	s.cache.Delete("dev_languages")

	s.logger.Info("批量删除开发语言成功")
	return nil
}

// BatchCreateProducts 批量创建产品
func (s *enhancedCMDBService) BatchCreateProducts(ctx context.Context, products []*models.ProductCreateRequest) ([]*models.Product, error) {
	if len(products) == 0 {
		return nil, fmt.Errorf("no products to create")
	}

	s.logger.WithField("count", len(products)).Info("批量创建产品")

	var results []*models.Product

	err := s.db.Transaction(func(tx *gorm.DB) error {
		for _, req := range products {
			// 检查产品代码是否已存在
			var existing models.Product
			if err := tx.Where("product_code = ?", req.ProductCode).First(&existing).Error; err == nil {
				return fmt.Errorf("product code %s already exists", req.ProductCode)
			}

			product := &models.Product{
				ProductCode: req.ProductCode,
				Name:        req.Name,
				RegionID:    req.RegionID,
				Description: req.Description,
				NamePrefix:  req.NamePrefix,
				Managers:    req.Managers,
			}

			if err := tx.Create(product).Error; err != nil {
				return fmt.Errorf("failed to create product %s: %w", req.ProductCode, err)
			}

			results = append(results, product)
		}

		return nil
	})

	if err != nil {
		s.logger.WithError(err).Error("批量创建产品失败")
		return nil, err
	}

	// 清除相关缓存
	s.cache.Delete("products")

	s.logger.WithField("created_count", len(results)).Info("批量创建产品成功")
	return results, nil
}

// BatchUpdateProducts 批量更新产品
func (s *enhancedCMDBService) BatchUpdateProducts(ctx context.Context, updates map[uint]*models.ProductUpdateRequest) error {
	if len(updates) == 0 {
		return fmt.Errorf("no updates provided")
	}

	s.logger.WithField("count", len(updates)).Info("批量更新产品")

	err := s.db.Transaction(func(tx *gorm.DB) error {
		for id, req := range updates {
			updateData := make(map[string]interface{})

			if req.Name != nil {
				updateData["name"] = *req.Name
			}
			if req.RegionID != nil {
				updateData["region_id"] = *req.RegionID
			}
			if req.Description != nil {
				updateData["description"] = *req.Description
			}
			if req.NamePrefix != nil {
				updateData["name_prefix"] = *req.NamePrefix
			}
			if req.Managers != nil {
				updateData["managers"] = *req.Managers
			}

			if len(updateData) > 0 {
				updateData["updated_at"] = time.Now()
				if err := tx.Model(&models.Product{}).Where("id = ?", id).Updates(updateData).Error; err != nil {
					return fmt.Errorf("failed to update product %d: %w", id, err)
				}
			}
		}

		return nil
	})

	if err != nil {
		s.logger.WithError(err).Error("批量更新产品失败")
		return err
	}

	// 清除相关缓存
	s.cache.Delete("products")

	s.logger.Info("批量更新产品成功")
	return nil
}

// BatchDeleteProducts 批量删除产品
func (s *enhancedCMDBService) BatchDeleteProducts(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return fmt.Errorf("no IDs provided")
	}

	s.logger.WithField("ids", ids).Info("批量删除产品")

	// 检查是否有关联的项目
	var projectCount int64
	if err := s.db.Model(&models.Project{}).Where("product_id IN ?", ids).Count(&projectCount).Error; err != nil {
		return fmt.Errorf("failed to check product dependencies: %w", err)
	}

	if projectCount > 0 {
		return fmt.Errorf("cannot delete products: %d projects are using these products", projectCount)
	}

	// 检查是否有关联的微应用
	var appCount int64
	if err := s.db.Model(&models.MicroApp{}).Where("product_id IN ?", ids).Count(&appCount).Error; err != nil {
		return fmt.Errorf("failed to check product dependencies: %w", err)
	}

	if appCount > 0 {
		return fmt.Errorf("cannot delete products: %d micro apps are using these products", appCount)
	}

	err := s.db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("id IN ?", ids).Delete(&models.Product{}).Error; err != nil {
			return fmt.Errorf("failed to delete products: %w", err)
		}
		return nil
	})

	if err != nil {
		s.logger.WithError(err).Error("批量删除产品失败")
		return err
	}

	// 清除相关缓存
	s.cache.Delete("products")

	s.logger.Info("批量删除产品成功")
	return nil
}

// =====缓存操作实现=====

// ClearCache 清除缓存
func (s *enhancedCMDBService) ClearCache(resourceType string) error {
	if resourceType == "" {
		s.cache.Clear()
		s.logger.Info("清除全部缓存")
	} else {
		s.cache.Delete(resourceType)
		s.logger.WithField("resource_type", resourceType).Info("清除指定资源缓存")
	}

	return nil
}

// RefreshCache 刷新缓存
func (s *enhancedCMDBService) RefreshCache(resourceType string) error {
	s.logger.WithField("resource_type", resourceType).Info("刷新缓存")

	switch resourceType {
	case "dev_languages":
		var languages []*models.DevLanguage
		if err := s.db.Find(&languages).Error; err != nil {
			return fmt.Errorf("failed to refresh dev languages cache: %w", err)
		}
		s.cache.Set("dev_languages", languages)

	case "products":
		var products []*models.Product
		if err := s.db.Preload("Region").Find(&products).Error; err != nil {
			return fmt.Errorf("failed to refresh products cache: %w", err)
		}
		s.cache.Set("products", products)

	case "environments":
		var environments []*models.Environment
		if err := s.db.Find(&environments).Error; err != nil {
			return fmt.Errorf("failed to refresh environments cache: %w", err)
		}
		s.cache.Set("environments", environments)

	default:
		return fmt.Errorf("unsupported resource type: %s", resourceType)
	}

	return nil
}

// =====统计分析实现=====

// GetResourceStats 获取资源统计
func (s *enhancedCMDBService) GetResourceStats(ctx context.Context) (*models.ResourceStats, error) {
	s.logger.Info("获取资源统计")

	cacheKey := "resource_stats"
	if cached, exists := s.cache.Get(cacheKey); exists {
		if stats, ok := cached.(*models.ResourceStats); ok {
			return stats, nil
		}
	}

	stats := &models.ResourceStats{
		Timestamp: time.Now(),
	}

	// 并发获取各种资源统计
	var wg sync.WaitGroup
	var mutex sync.Mutex
	errors := make([]error, 0)

	// 开发语言统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		var count int64
		if err := s.db.Model(&models.DevLanguage{}).Count(&count).Error; err != nil {
			mutex.Lock()
			errors = append(errors, fmt.Errorf("failed to count dev languages: %w", err))
			mutex.Unlock()
			return
		}
		mutex.Lock()
		stats.DevLanguages = count
		mutex.Unlock()
	}()

	// 产品统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		var count int64
		if err := s.db.Model(&models.Product{}).Count(&count).Error; err != nil {
			mutex.Lock()
			errors = append(errors, fmt.Errorf("failed to count products: %w", err))
			mutex.Unlock()
			return
		}
		mutex.Lock()
		stats.Products = count
		mutex.Unlock()
	}()

	// 项目统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		var count int64
		if err := s.db.Model(&models.Project{}).Count(&count).Error; err != nil {
			mutex.Lock()
			errors = append(errors, fmt.Errorf("failed to count projects: %w", err))
			mutex.Unlock()
			return
		}
		mutex.Lock()
		stats.Projects = count
		mutex.Unlock()
	}()

	// 微应用统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		var count int64
		if err := s.db.Model(&models.MicroApp{}).Count(&count).Error; err != nil {
			mutex.Lock()
			errors = append(errors, fmt.Errorf("failed to count micro apps: %w", err))
			mutex.Unlock()
			return
		}
		mutex.Lock()
		stats.MicroApps = count
		mutex.Unlock()
	}()

	// 环境统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		var count int64
		if err := s.db.Model(&models.Environment{}).Count(&count).Error; err != nil {
			mutex.Lock()
			errors = append(errors, fmt.Errorf("failed to count environments: %w", err))
			mutex.Unlock()
			return
		}
		mutex.Lock()
		stats.Environments = count
		mutex.Unlock()
	}()

	// K8s集群统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		var count int64
		if err := s.db.Model(&models.KubernetesCluster{}).Count(&count).Error; err != nil {
			mutex.Lock()
			errors = append(errors, fmt.Errorf("failed to count kubernetes clusters: %w", err))
			mutex.Unlock()
			return
		}
		mutex.Lock()
		stats.KubernetesClusters = count
		mutex.Unlock()
	}()

	wg.Wait()

	if len(errors) > 0 {
		return nil, fmt.Errorf("failed to get resource stats: %v", errors)
	}

	// 缓存结果
	s.cache.Set(cacheKey, stats)

	return stats, nil
}

// GetUsageAnalytics 获取使用分析
func (s *enhancedCMDBService) GetUsageAnalytics(ctx context.Context, req *models.AnalyticsRequest) (*models.AnalyticsResponse, error) {
	s.logger.WithFields(logrus.Fields{
		"start_date": req.StartDate,
		"end_date":   req.EndDate,
		"group_by":   req.GroupBy,
	}).Info("获取使用分析")

	response := &models.AnalyticsResponse{
		Period:    fmt.Sprintf("%s to %s", req.StartDate.Format("2006-01-02"), req.EndDate.Format("2006-01-02")),
		GroupBy:   req.GroupBy,
		Timestamp: time.Now(),
	}

	// 这里可以根据需求实现具体的分析逻辑
	// 例如：按时间分组的资源创建趋势、使用频率等

	return response, nil
}

// =====导入导出实现=====

// ExportResources 导出资源
func (s *enhancedCMDBService) ExportResources(ctx context.Context, req *models.ExportRequest) ([]byte, error) {
	s.logger.WithFields(logrus.Fields{
		"format": req.Format,
		"filter": req.Filter,
	}).Info("导出资源")

	// 根据不同的资源类型和格式进行导出
	switch req.Format {
	case "json":
		return s.exportToJSON(ctx, req)
	case "yaml":
		return s.exportToYAML(ctx, req)
	case "csv":
		return s.exportToCSV(ctx, req)
	default:
		return nil, fmt.Errorf("unsupported export format: %s", req.Format)
	}
}

// exportToJSON 导出为JSON格式
func (s *enhancedCMDBService) exportToJSON(ctx context.Context, req *models.ExportRequest) ([]byte, error) {
	data := make(map[string]interface{})

	// 导出开发语言
	var languages []*models.DevLanguage
	if err := s.db.Find(&languages).Error; err != nil {
		return nil, fmt.Errorf("failed to export dev languages: %w", err)
	}
	data["dev_languages"] = languages

	// 导出产品
	var products []*models.Product
	if err := s.db.Preload("Region").Find(&products).Error; err != nil {
		return nil, fmt.Errorf("failed to export products: %w", err)
	}
	data["products"] = products

	// 导出环境
	var environments []*models.Environment
	if err := s.db.Find(&environments).Error; err != nil {
		return nil, fmt.Errorf("failed to export environments: %w", err)
	}
	data["environments"] = environments

	return json.MarshalIndent(data, "", "  ")
}

// exportToYAML 导出为YAML格式
func (s *enhancedCMDBService) exportToYAML(ctx context.Context, req *models.ExportRequest) ([]byte, error) {
	// 这里需要使用YAML库进行序列化
	// 暂时返回JSON格式
	return s.exportToJSON(ctx, req)
}

// exportToCSV 导出为CSV格式
func (s *enhancedCMDBService) exportToCSV(ctx context.Context, req *models.ExportRequest) ([]byte, error) {
	// 这里需要实现CSV格式的导出
	// 暂时返回错误
	return nil, fmt.Errorf("CSV export not implemented yet")
}

// ImportResources 导入资源
func (s *enhancedCMDBService) ImportResources(ctx context.Context, req *models.ImportRequest) (*models.ImportResult, error) {
	s.logger.WithField("format", req.Format).Info("导入资源")

	result := &models.ImportResult{
		Timestamp: time.Now(),
		Format:    req.Format,
	}

	switch req.Format {
	case "json":
		return s.importFromJSON(ctx, req, result)
	case "yaml":
		return s.importFromYAML(ctx, req, result)
	default:
		return nil, fmt.Errorf("unsupported import format: %s", req.Format)
	}
}

// importFromJSON 从JSON导入
func (s *enhancedCMDBService) importFromJSON(ctx context.Context, req *models.ImportRequest, result *models.ImportResult) (*models.ImportResult, error) {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(req.Data), &data); err != nil {
		return nil, fmt.Errorf("failed to parse JSON data: %w", err)
	}

	// 这里实现具体的导入逻辑
	result.Success = true
	result.Message = "Import completed successfully"

	return result, nil
}

// importFromYAML 从YAML导入
func (s *enhancedCMDBService) importFromYAML(ctx context.Context, req *models.ImportRequest, result *models.ImportResult) (*models.ImportResult, error) {
	// 这里需要使用YAML库进行反序列化
	return nil, fmt.Errorf("YAML import not implemented yet")
}

// =====关系管理实现=====

// GetResourceDependencies 获取资源依赖关系
func (s *enhancedCMDBService) GetResourceDependencies(ctx context.Context, resourceType string, resourceID uint) (*models.DependencyGraph, error) {
	s.logger.WithFields(logrus.Fields{
		"resource_type": resourceType,
		"resource_id":   resourceID,
	}).Info("获取资源依赖关系")

	graph := &models.DependencyGraph{
		ResourceType: resourceType,
		ResourceID:   resourceID,
		Dependencies: make([]models.Dependency, 0),
	}

	switch resourceType {
	case "product":
		return s.getProductDependencies(ctx, resourceID, graph)
	case "project":
		return s.getProjectDependencies(ctx, resourceID, graph)
	case "dev_language":
		return s.getDevLanguageDependencies(ctx, resourceID, graph)
	default:
		return nil, fmt.Errorf("unsupported resource type: %s", resourceType)
	}
}

// getProductDependencies 获取产品依赖关系
func (s *enhancedCMDBService) getProductDependencies(ctx context.Context, productID uint, graph *models.DependencyGraph) (*models.DependencyGraph, error) {
	// 查找依赖此产品的项目
	var projects []*models.Project
	if err := s.db.Where("product_id = ?", productID).Find(&projects).Error; err != nil {
		return nil, fmt.Errorf("failed to get dependent projects: %w", err)
	}

	for _, project := range projects {
		graph.Dependencies = append(graph.Dependencies, models.Dependency{
			Type: "project",
			ID:   project.ID,
			Name: project.Name,
		})
	}

	// 查找依赖此产品的微应用
	var apps []*models.MicroApp
	if err := s.db.Where("product_id = ?", productID).Find(&apps).Error; err != nil {
		return nil, fmt.Errorf("failed to get dependent micro apps: %w", err)
	}

	for _, app := range apps {
		graph.Dependencies = append(graph.Dependencies, models.Dependency{
			Type: "micro_app",
			ID:   app.ID,
			Name: app.Name,
		})
	}

	return graph, nil
}

// getProjectDependencies 获取项目依赖关系
func (s *enhancedCMDBService) getProjectDependencies(ctx context.Context, projectID uint, graph *models.DependencyGraph) (*models.DependencyGraph, error) {
	// 查找依赖此项目的微应用
	var apps []*models.MicroApp
	if err := s.db.Where("project_id = ?", projectID).Find(&apps).Error; err != nil {
		return nil, fmt.Errorf("failed to get dependent micro apps: %w", err)
	}

	for _, app := range apps {
		graph.Dependencies = append(graph.Dependencies, models.Dependency{
			Type: "micro_app",
			ID:   app.ID,
			Name: app.Name,
		})
	}

	return graph, nil
}

// getDevLanguageDependencies 获取开发语言依赖关系
func (s *enhancedCMDBService) getDevLanguageDependencies(ctx context.Context, languageID uint, graph *models.DependencyGraph) (*models.DependencyGraph, error) {
	// 首先获取语言代码
	var language models.DevLanguage
	if err := s.db.First(&language, languageID).Error; err != nil {
		return nil, fmt.Errorf("failed to get dev language: %w", err)
	}

	// 查找使用此语言的微应用
	var apps []*models.MicroApp
	if err := s.db.Where("language_code = ?", language.LanguageCode).Find(&apps).Error; err != nil {
		return nil, fmt.Errorf("failed to get dependent micro apps: %w", err)
	}

	for _, app := range apps {
		graph.Dependencies = append(graph.Dependencies, models.Dependency{
			Type: "micro_app",
			ID:   app.ID,
			Name: app.Name,
		})
	}

	return graph, nil
}

// ValidateResourceDeletion 验证资源删除
func (s *enhancedCMDBService) ValidateResourceDeletion(ctx context.Context, resourceType string, resourceID uint) (*models.ValidationResult, error) {
	s.logger.WithFields(logrus.Fields{
		"resource_type": resourceType,
		"resource_id":   resourceID,
	}).Info("验证资源删除")

	result := &models.ValidationResult{
		CanDelete: true,
		Warnings:  make([]string, 0),
		Errors:    make([]string, 0),
	}

	// 获取依赖关系
	graph, err := s.GetResourceDependencies(ctx, resourceType, resourceID)
	if err != nil {
		result.CanDelete = false
		result.Errors = append(result.Errors, fmt.Sprintf("Failed to check dependencies: %v", err))
		return result, nil
	}

	if len(graph.Dependencies) > 0 {
		result.CanDelete = false
		var dependencyNames []string
		for _, dep := range graph.Dependencies {
			dependencyNames = append(dependencyNames, fmt.Sprintf("%s:%s", dep.Type, dep.Name))
		}
		result.Errors = append(result.Errors, fmt.Sprintf("Cannot delete %s: has %d dependencies [%s]",
			resourceType, len(graph.Dependencies), strings.Join(dependencyNames, ", ")))
	}

	return result, nil
}
