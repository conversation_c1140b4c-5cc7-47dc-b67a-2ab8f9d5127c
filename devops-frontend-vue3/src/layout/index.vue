<template>
  <el-container class="layout-container">
    <!-- 顶部导航 -->
    <el-header class="layout-header">
      <div class="header-left">
        <img src="@/assets/logo.png" alt="Logo" class="logo" />
        <h1>DevOps管理平台</h1>
      </div>
      <div class="header-right">
        <el-dropdown>
          <span class="user-dropdown">
            <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
            <span>{{ userStore.user?.username || 'Admin' }}</span>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>个人设置</el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '240px'" class="layout-sidebar">
        <el-menu :default-active="$route.path" router unique-opened class="sidebar-menu" :collapse="isCollapse">
          <el-menu-item index="/dashboard">
            <el-icon>
              <DataBoard />
            </el-icon>
            <span>仪表板</span>
          </el-menu-item>

          <el-sub-menu index="/microapp">
            <template #title>
              <el-icon>
                <Platform />
              </el-icon>
              <span>微应用管理</span>
            </template>
            <!-- <el-menu-item index="/microapp/cmdbDashboard">
              <el-icon>
                <DataBoard />
              </el-icon>
              <span>CMDB概览</span>
            </el-menu-item> -->
            <el-menu-item index="/microapp/list">
              <el-icon>
                <Memo />
              </el-icon>
              <span>应用列表</span>
            </el-menu-item>
            <!-- <el-menu-item index="/microapp/instance">
              <el-icon><Connection /></el-icon>
              <span>应用实例</span>
            </el-menu-item> -->
            <el-menu-item index="/microapp/language">
              <el-icon>
                <Document />
              </el-icon>
              <span>开发语言</span>
            </el-menu-item>
            <el-menu-item index="/microapp/environment">
              <el-icon>
                <ScaleToOriginal />
              </el-icon>
              <span>环境管理</span>
            </el-menu-item>
            <el-menu-item index="/microapp/project">
              <el-icon>
                <Files />
              </el-icon>
              <span>项目管理</span>
            </el-menu-item>
            <el-menu-item index="/microapp/product">
              <el-icon>
                <Goods />
              </el-icon>
              <span>产品管理</span>
            </el-menu-item>
            <el-menu-item index="/microapp/region">
              <el-icon>
                <MapLocation />
              </el-icon>
              <span>区域管理</span>
            </el-menu-item>
            <el-menu-item index="/microapp/team">
              <el-icon>
                <User />
              </el-icon>
              <span>团队管理</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/cicd">
            <template #title>
              <el-icon>
                <Operation />
              </el-icon>
              <span>CICD管理</span>
            </template>
            <el-menu-item index="/cicd/dashboard">
              <el-icon>
                <TrendCharts />
              </el-icon>
              <span>构建概览</span>
            </el-menu-item>
            <el-menu-item index="/cicd/pipeline">
              <el-icon>
                <Sort />
              </el-icon>
              <span>流水线</span>
            </el-menu-item>
            <el-menu-item index="/cicd/pipeline/task">
              <el-icon>
                <Sort />
              </el-icon>
              <span>流水线任务</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/kubernetes">
            <template #title>
              <el-icon>
                <Monitor />
              </el-icon>
              <span>Kubernetes</span>
            </template>
            <el-menu-item v-if="!clusterId" index="/kubernetes/cluster">
              <el-icon>
                <Management />
              </el-icon>
              <span>集群管理</span>
            </el-menu-item>
            <el-sub-menu v-else index="/kubernetes/cluster">
              <template #title>
                <el-icon>
                  <Management />
                </el-icon>
                <span>集群管理</span>
              </template>
              <el-menu-item :index="`/kubernetes/cluster/${clusterId}`">
                <el-icon>
                  <Postcard />
                </el-icon>
                <span>集群详情</span>
              </el-menu-item>
              <el-menu-item :index="`/kubernetes/cluster/${clusterId}/workload`">
                <el-icon>
                  <Cpu />
                </el-icon>
                <span>工作负载</span>
              </el-menu-item>
            </el-sub-menu>
            <!-- <el-menu-item index="/kubernetes/cluster">
              <el-icon><Grid /></el-icon>
              <span>集群管理</span>
            </el-menu-item> -->
          </el-sub-menu>

          <el-sub-menu index="/workflow">
            <template #title>
              <el-icon>
                <Operation />
              </el-icon>
              <span>工作流管理</span>
            </template>
            <el-menu-item index="/workflow/list">
              <el-icon>
                <List />
              </el-icon>
              <span>工作流列表</span>
            </el-menu-item>
            <el-sub-menu index="/workflow/my">
              <template #title>
                <el-icon>
                  <User />
                </el-icon>
                <span>我的工作流</span>
              </template>
              <el-menu-item index="/workflow/my-request">
                <el-icon>
                  <Document />
                </el-icon>
                <span>我发起的</span>
              </el-menu-item>
              <el-menu-item index="/workflow/my-upcoming">
                <el-icon>
                  <Clock />
                </el-icon>
                <span>我的待办</span>
              </el-menu-item>
              <el-menu-item index="/workflow/my-related">
                <el-icon>
                  <Connection />
                </el-icon>
                <span>我相关的</span>
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item index="/workflow/template">
              <el-icon>
                <Files />
              </el-icon>
              <span>模板管理</span>
            </el-menu-item>
            <el-menu-item index="/workflow/category">
              <el-icon>
                <Folder />
              </el-icon>
              <span>分类管理</span>
            </el-menu-item>
            <el-menu-item index="/workflow/callback">
              <el-icon>
                <Setting />
              </el-icon>
              <span>回调管理</span>
            </el-menu-item>
            <el-menu-item index="/workflow/designer">
              <el-icon>
                <ScaleToOriginal />
              </el-icon>
              <span>流程设计器</span>
            </el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="/system">
            <template #title>
              <el-icon>
                <Setting />
              </el-icon>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/system/user">
              <el-icon>
                <User />
              </el-icon>
              <span>用户管理</span>
            </el-menu-item>
            <!-- <el-menu-item index="/system/role">
              <el-icon><User /></el-icon>
              <span>角色管理</span>
            </el-menu-item> -->
            <el-menu-item index="/system/rolegroup">
              <el-icon>
                <UserFilled />
              </el-icon>
              <span>角色组管理</span>
            </el-menu-item>
            <el-menu-item index="/system/permission">
              <el-icon>
                <Guide />
              </el-icon>
              <span>权限管理</span>
            </el-menu-item>
            <el-menu-item index="/system/menu">
              <el-icon>
                <Menu />
              </el-icon>
              <span>菜单管理</span>
            </el-menu-item>
            <el-menu-item index="/system/organization">
              <el-icon>
                <OfficeBuilding />
              </el-icon>
              <span>组织管理</span>
            </el-menu-item>
            <!-- <el-menu-item index="/system/oauth">
              <el-icon><Key /></el-icon>
              <span>OAuth配置</span>
            </el-menu-item>
            <el-menu-item index="/system/ldap">
              <el-icon><Connection /></el-icon>
              <span>LDAP配置</span>
            </el-menu-item> -->
            <el-menu-item index="/system/audit">
              <el-icon>
                <Document />
              </el-icon>
              <span>审计日志</span>
            </el-menu-item>
            <!-- <el-menu-item index="/system/log">
              <el-icon><Document /></el-icon>
              <span>日志管理</span>
            </el-menu-item> -->
            <el-menu-item index="/system/settings">
              <el-icon>
                <Setting />
              </el-icon>
              <span>系统设置</span>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>

        <!-- 侧边栏底部折叠按钮 -->
        <div class="sidebar-footer" :class="{ 'collapsed': isCollapse }">
          <el-button type="text" @click="toggleCollapse" :title="isCollapse ? '展开菜单' : '收起菜单'">
            <el-icon v-if="isCollapse">
              <Expand />
            </el-icon>
            <el-icon v-else>
              <Fold />
            </el-icon>
            <span v-if="!isCollapse">收起菜单</span>
          </el-button>
        </div>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="layout-main" :class="{ 'collapsed': isCollapse }">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { useUserStore } from '../stores/user'
import { useRouter } from 'vue-router'
import { computed, ref } from 'vue'
import {
  Management,
  DataBoard,
  Platform,
  Memo,
  Connection,
  Document,
  ScaleToOriginal,
  Files,
  Goods,
  MapLocation,
  User,
  UserFilled,
  Menu,
  OfficeBuilding,
  Key,
  Operation,
  TrendCharts,
  Sort,
  Monitor,
  Grid,
  Cpu,
  Expand,
  Fold,
  Clock,
  List,
  Folder,
  Setting,
  Guide,
  Postcard
} from '@element-plus/icons-vue'

const userStore = useUserStore()
const router = useRouter()
const isCollapse = ref(false)

// computed计算url中是否包含cluster_id
const clusterId = computed(() => {
  if (router.currentRoute.value.path.startsWith('/kubernetes/cluster')) {
    return router.currentRoute.value.params.cluster_id || router.currentRoute.value.params.id
  }
  return null
})

const handleLogout = () => {
  userStore.logout()
  router.push('/login')
}

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  overflow: hidden;
}

.layout-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border-bottom: 1px solid var(--border-color);
  padding: 0 20px;
  height: 60px;

  .header-left {
    display: flex;
    align-items: center;

    .logo {
      width: 32px;
      height: 32px;
      margin-right: 12px;
    }

    h1 {
      margin: 0;
      font-size: 18px;
      color: var(--text-primary);
    }
  }

  .header-right {
    .user-dropdown {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
    }
  }
}

.layout-sidebar {
  position: fixed;
  top: 60px;
  left: 0;
  bottom: 0;
  z-index: 999;
  background: white;
  border-right: 1px solid var(--border-color);
  transition: width 0.3s;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .sidebar-menu {
    border: none;
    height: calc(100% - 50px);
    transition: all 0.3s;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .sidebar-footer {
    height: 50px;
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    background-color: white;
    margin-top: auto;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;

    .el-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      height: 100%;
      width: 100%;
      font-size: 14px;
      color: var(--text-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }

    &.collapsed {
      padding: 0;

      .el-button {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.layout-main {
  margin-top: 60px;
  margin-left: 240px;
  background: var(--bg-color);
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 60px);
  transition: margin-left 0.3s;
}

// 侧边栏收起时调整主内容区的margin
.layout-main.collapsed {
  margin-left: 64px;
}
</style>
