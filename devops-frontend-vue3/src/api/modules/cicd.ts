import request from '@/utils/request'

// 构建任务接口
export interface BuildTask {
    id: number
    app_info_id?: number
    app_id?: number
    branch: string
    commit_id?: string
    commit_message?: string
    commit_url?: string
    status: string
    duration?: number
    created_by?: {
        id: number
        name: string
        username: string
        avatar?: string
    }
    created_at: string
    updated_at: string
    logs?: string

    // 关联应用信息
    app?: {
        id: number
        name: string
        app_id: string
        project?: {
            id: number
            name: string
        }
    }

    // 前端兼容字段
    project?: {
        name: string
        avatar?: string
    }
}

// 部署任务接口
export interface DeployTask {
    id: number
    app_id?: number
    app_info_id?: number
    environment: string
    version: string
    status: string
    replicas?: number
    strategy?: string
    created_by?: {
        id: number
        name: string
        username: string
    }
    created_at: string
    updated_at: string

    // 关联应用信息
    app?: {
        id: number
        name: string
        app_id: string
    }
}

// 发布工单接口
export interface ReleaseOrder {
    id: number
    title: string
    description?: string
    status: string
    environment: string
    created_by?: {
        id: number
        name: string
        username: string
    }
    created_at: string
    updated_at: string

    // 工单中的应用
    apps?: {
        id: number
        name: string
        status: string
        version?: string
    }[]
}

export default {
    // 构建相关
    getCdImage(params: any) {
        return request.get('/v1/cicd/image/', params)
    },

    // 获取构建列表
    getBuilds(params: any) {
        return request.get('/v1/cicd/builds', params)
    },

    // 获取构建详情
    getBuild(id: number) {
        return request.get(`/v1/cicd/builds/${id}`)
    },

    // 创建构建
    createBuild(params: any) {
        return request.post('/v1/cicd/builds', params)
    },

    // 取消构建
    cancelBuild(id: number) {
        return request.post(`/v1/cicd/builds/cancel/${id}/`)
    },

    // 获取构建日志
    getBuildLogs(buildId: number) {
        return request.get(`/v1/cicd/${buildId}/logs`)
    },

    // 持续集成
    ciApp(params: any) {
        return request.post(`/v1/cicd/builds/${params.id}/ci/`, params)
    },

    // 部署相关
    getDeploys(params: any) {
        return request.get('/v1/cicd/deploys', params)
    },

    // 获取部署详情
    getDeploy(id: number) {
        return request.get(`/v1/cicd/deploys/${id}`)
    },

    // 创建部署
    createDeploy(params: any) {
        return request.post('/v1/cicd/deploys', params)
    },

    // 取消部署
    cancelDeploy(id: number) {
        return request.post(`/v1/cicd/deploys/${id}/cancel`)
    },

    // 持续部署
    continuedDeploy(params: any) {
        return request.post('/v1/cicd/deploys/deploy/', params)
    },

    // 发布工单相关
    getCicdOrder(params: any) {
        return request.get('/v1/cicd/orders', params)
    },

    getCicdOrderDetails(id: number) {
        return request.get(`/v1/cicd/orders/${id}`)
    },

    createOrder(params: any) {
        return request.post('/v1/cicd/orders', params)
    },

    cancelOrder(id: number) {
        return request.post(`/v1/cicd/orders/${id}/cancel`)
    },

    // 回滚相关
    getRollbackHistory(params: any) {
        return request.get('/v1/cicd/rollback/history', params)
    },

    createRollback(params: any) {
        return request.post('/v1/cicd/rollback', params)
    },

    // 流水线相关
    getPipelineTasks(params: any) {
        return request.get('/v1/cicd/pipeline/tasks', { params })
    },

    createPipelineTask(params: any) {
        return request.post('/v1/cicd/pipeline/tasks', params)
    },

    updatePipelineTask(params: any) {
        return request.put('/v1/cicd/pipeline/tasks', params)
    },

    deletePipelineTask(id: number) {
        return request.delete(`/v1/cicd/pipeline/tasks/${id}`)
    },

    syncPipelineTasks() {
        return request.post('/v1/cicd/pipeline/tasks/sync')
    }
} 