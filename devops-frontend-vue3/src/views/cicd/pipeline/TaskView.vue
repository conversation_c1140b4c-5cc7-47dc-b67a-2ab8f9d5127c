<template>
  <div class="task-page">
    <div class="page-header">
      <h3>Tekton任务管理</h3>
      <div class="header-actions">
        <el-button type="primary" @click="openForm()">新建任务</el-button>
        <el-button type="info" @click="syncTasks" :loading="syncing">同步Tekton任务</el-button>
      </div>
    </div>
    <el-card>
      <el-table :data="tasks" v-loading="loading" border stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="namespace" label="命名空间" width="120" />
        <el-table-column prop="task_name" label="任务名称" min-width="180" />
        <el-table-column prop="label" label="标签" width="120" />
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="openForm(row)">编辑</el-button>
            <el-popconfirm title="确定删除此任务吗？" @confirm="deleteTask(row)">
              <template #reference>
                <el-button size="small" type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadTasks"
          @current-change="loadTasks"
        />
      </div>
    </el-card>
    <el-dialog v-model="formDialog.visible" :title="formDialog.title" width="500px" :close-on-click-modal="false">
      <el-form ref="formRef" :model="form" :rules="formRules" label-width="100px">
        <el-form-item label="命名空间" prop="namespace">
          <el-input v-model="form.namespace" placeholder="如: default" />
        </el-form-item>
        <el-form-item label="任务名称" prop="task_name">
          <el-input v-model="form.task_name" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="标签" prop="label">
          <el-input v-model="form.label" placeholder="可选" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="可选" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="formDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="formDialog.loading">{{ formDialog.mode === 'edit' ? '更新' : '创建' }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { default as api } from '../../../api'
import { handleAPIResponse } from '../../../utils/response'

const tasks = ref<any[]>([])
const loading = ref(false)
const syncing = ref(false)
const pagination = reactive({ page: 1, pageSize: 20, total: 0 })

const loadTasks = async () => {
  loading.value = true
  try {
    const res = await api.getPipelineTasks({ page: pagination.page, page_size: pagination.pageSize })
    const result = handleAPIResponse(res) as any
    console.log(result, 'result')
    tasks.value = result.items
    pagination.total = result.total
    pagination.page = result.page
    pagination.pageSize = result.page_size
  } catch (e) {
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

const syncTasks = async () => {
  syncing.value = true
  try {
    await api.syncPipelineTasks()
    ElMessage.success('同步成功')
    loadTasks()
  } catch (e) {
    ElMessage.error('同步失败')
  } finally {
    syncing.value = false
  }
}

// 表单相关
const formDialog = reactive({ visible: false, title: '', mode: 'create', loading: false })
const form = reactive({ id: 0, namespace: 'default', task_name: '', label: '', description: '' })
const formRef = ref()
const formRules = {
  namespace: [{ required: true, message: '请输入命名空间', trigger: 'blur' }],
  task_name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }]
}
const openForm = (row?: any) => {
  if (row) {
    Object.assign(form, row)
    formDialog.title = '编辑任务'
    formDialog.mode = 'edit'
  } else {
    Object.assign(form, { id: 0, namespace: 'default', task_name: '', label: '', description: '' })
    formDialog.title = '新建任务'
    formDialog.mode = 'create'
  }
  formDialog.visible = true
}
const submitForm = async () => {
  formDialog.loading = true
  try {
    if (formDialog.mode === 'edit') {
      await api.updatePipelineTask(form)
      ElMessage.success('更新成功')
    } else {
      await api.createPipelineTask(form)
      ElMessage.success('创建成功')
    }
    formDialog.visible = false
    loadTasks()
  } catch (e) {
    ElMessage.error('操作失败')
  } finally {
    formDialog.loading = false
  }
}
const deleteTask = async (row: any) => {
  try {
    await api.deletePipelineTask(row.id)
    ElMessage.success('删除成功')
    loadTasks()
  } catch (e) {
    ElMessage.error('删除失败')
  }
}
onMounted(() => {
  loadTasks()
})
</script>
<style scoped>
.task-page { padding: 20px; }
.page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
.header-actions { display: flex; gap: 12px; }
.pagination-wrapper { margin-top: 20px; text-align: right; }
</style>