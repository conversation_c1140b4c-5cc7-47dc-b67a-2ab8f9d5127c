<template>
  <div class="region-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h3>区域管理</h3>
        <p class="page-description">管理系统中的区域信息配置</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCreate" :icon="Plus">
          新建区域
        </el-button>
      </div>
    </div>
    
    <!-- 搜索筛选区 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form-wrapper">
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="区域名称">
            <el-input 
              v-model="searchForm.name"
              placeholder="请输入区域名称"
              clearable
              @keyup.enter="handleSearch"
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
            <el-button @click="handleReset" :icon="Refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <!-- 数据表格/卡片 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <div class="table-title">
            <span>区域列表</span>
            <el-tag type="info" size="small" class="ml-2">
              共 {{ pagination.total }} 条
            </el-tag>
          </div>
        </div>
      </template>

      <!-- PC端表格 -->
      <div class="desktop-table">
        <el-table 
          :data="tableData" 
          v-loading="loading"
          border
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="名称" min-width="150" />
          <el-table-column prop="region_code" label="区域代码" min-width="150" />
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="sort_order" label="排序" width="100" />
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="更新时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button size="small" type="primary" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-popconfirm 
                title="确定删除此区域吗？"
                @confirm="handleDelete(row)"
              >
                <template #reference>
                  <el-button size="small" type="danger">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 移动端卡片 -->
      <div class="mobile-cards">
        <div v-if="tableData.length === 0" class="empty-state">
          <el-empty description="暂无区域数据">
            <el-button type="primary" @click="handleCreate" :icon="Plus">
              新建区域
            </el-button>
          </el-empty>
        </div>
        
        <div v-else v-loading="loading">
          <el-card 
            v-for="region in tableData" 
            :key="region.id" 
            class="region-card"
            shadow="hover"
          >
            <template #header>
              <div class="card-header">
                <div class="card-title">
                  <span class="region-name">{{ region.name }}</span>
                  <el-tag type="primary" size="small">ID: {{ region.id }}</el-tag>
                </div>
                <el-dropdown trigger="click">
                  <el-button type="text" class="more-btn">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleEdit(region)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item @click="handleDelete(region)" divided>
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>

            <div class="card-content">
              <div class="info-item">
                <span class="label">区域代码:</span>
                <span class="value">{{ region.region_code || '-' }}</span>
              </div>
              
              <div class="info-item">
                <span class="label">描述:</span>
                <span class="value">{{ region.description || '-' }}</span>
              </div>
              
              <div class="info-item">
                <span class="label">排序:</span>
                <span class="value">{{ region.sort_order }}</span>
              </div>
              
              <div class="info-item">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatDateTime(region.created_at) }}</span>
              </div>
              
              <div class="info-item">
                <span class="label">更新时间:</span>
                <span class="value">{{ formatDateTime(region.updated_at) }}</span>
              </div>
            </div>

            <template #footer>
              <div class="card-footer">
                <el-button type="primary" size="small" @click="handleEdit(region)">
                  编辑
                </el-button>
                <el-popconfirm 
                  title="确定删除此区域吗？"
                  @confirm="handleDelete(region)"
                >
                  <template #reference>
                    <el-button type="danger" size="small">
                      删除
                    </el-button>
                  </template>
                </el-popconfirm>
              </div>
            </template>
          </el-card>
        </div>
      </div>
      
      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 编辑/新增对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'create' ? '新增区域' : '编辑区域'"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="区域代码" prop="region_code">
          <el-input v-model="form.region_code" placeholder="请输入区域代码，如: china" />
        </el-form-item>
        
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入区域名称，如: 中国" />
        </el-form-item>
        
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="form.sort_order" :min="0" style="width: 100%" />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入区域描述信息" 
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { formatDateTime } from '../../../utils/date'
import { regionApi, type Region } from '../../../api/modules/cmdb'
import { Plus, Search, Refresh, MoreFilled, Edit, Delete } from '@element-plus/icons-vue'
import { handleAPIResponse } from '../../../utils/response'

// 表格数据
const tableData = ref<Region[]>([])
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  name: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 对话框控制
const dialogVisible = ref(false)
const dialogType = ref<'create' | 'edit'>('create')
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  id: 0,
  name: '',
  region_code: '',
  description: '',
  sort_order: 0
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入区域代码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  region_code: [
    { required: true, message: '请输入区域名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 获取区域列表
const fetchRegions = async () => {
  loading.value = true
  try {
    const res = await regionApi.getRegions({
      page: pagination.page,
      page_size: pagination.pageSize,
      name: searchForm.name
    })
    
    const result = handleAPIResponse(res) as { items: Region[], total: number }
    if (result?.items) {
      tableData.value = result.items
      pagination.total = result.total || 0
    }
  } catch (error) {
    console.error('获取区域列表失败:', error)
    ElMessage.error('获取区域列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchRegions()
}

// 重置搜索
const handleReset = () => {
  searchForm.name = ''
  pagination.page = 1
  fetchRegions()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchRegions()
}

// 分页页码变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchRegions()
}

// 新建区域
const handleCreate = () => {
  dialogType.value = 'create'
  form.id = 0
  form.name = ''
  form.region_code = ''
  form.description = ''
  form.sort_order = 0
  dialogVisible.value = true
}

// 编辑区域
const handleEdit = async (region: Region) => {
  dialogType.value = 'edit'
  try {
    const res = await regionApi.getRegion(region.id)
    const regionData = handleAPIResponse(res) as any
    if (regionData) {
      form.id = regionData.id || 0
      form.name = regionData.name || ''
      form.region_code = regionData.region_code || ''
      form.description = regionData.description || ''
      form.sort_order = typeof regionData.sort_order === 'number' ? regionData.sort_order : 0
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('获取区域详情失败:', error)
    ElMessage.error('获取区域详情失败')
  }
}

// 删除区域
const handleDelete = async (row: Region) => {
  try {
    await regionApi.deleteRegion(row.id)
    ElMessage.success('删除成功')
    fetchRegions() // 刷新列表
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'create') {
          await regionApi.createRegion({
            name: form.name,
            region_code: form.region_code,
            description: form.description,
            sort_order: form.sort_order
          })
          ElMessage.success('创建成功')
        } else {
          await regionApi.updateRegion(form.id, {
            name: form.name,
            region_code: form.region_code,
            description: form.description,
            sort_order: form.sort_order
          })
          ElMessage.success('更新成功')
        }
        
        dialogVisible.value = false
        fetchRegions() // 刷新列表
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败')
      }
    }
  })
}

onMounted(() => {
  fetchRegions()
})
</script>

<style lang="scss" scoped>
.region-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left {
  h3 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form-wrapper {
  padding: 10px 0;
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.ml-2 {
  margin-left: 8px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

// PC端表格显示，移动端隐藏
.desktop-table {
  display: block;
}

.mobile-cards {
  display: none;
}

// 移动端卡片样式
.region-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.region-name {
  font-weight: 500;
  font-size: 16px;
}

.more-btn {
  padding: 4px;
}

.card-content {
  padding: 8px 0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  
  .label {
    min-width: 80px;
    color: #606266;
    font-size: 14px;
    font-weight: 500;
  }
  
  .value {
    color: #303133;
    font-size: 14px;
    margin-left: 12px;
    flex: 1;
  }
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

// 移动端样式
@media (max-width: 768px) {
  .region-page {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .header-right {
    width: 100%;
  }
  
  .search-form {
    flex-direction: column;
    
    .el-form-item {
      margin-bottom: 12px;
      width: 100%;
    }
  }
  
  // 移动端隐藏表格，显示卡片
  .desktop-table {
    display: none;
  }
  
  .mobile-cards {
    display: block;
  }
  
  .pagination-wrapper {
    .el-pagination {
      justify-content: center;
      
      .el-pagination__sizes,
      .el-pagination__jump {
        display: none;
      }
    }
  }
}

// 平板端适配
@media (max-width: 1024px) and (min-width: 769px) {
  .region-page {
    padding: 15px;
  }
}
</style> 