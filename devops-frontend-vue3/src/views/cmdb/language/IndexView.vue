<template>
  <div class="language-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>开发语言管理</h2>
        <p>管理系统支持的开发语言和相关配置</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleCreate">
          <el-icon>
            <Plus />
          </el-icon>
          新增语言
        </el-button>
      </div>
    </div>

    <!-- 数据卡片 -->
    <el-card class="cards-container" shadow="never" v-loading="loading">
      <template #header>
        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-form :model="searchForm" inline>
            <el-form-item label="">
              <el-input v-model="searchForm.search" placeholder="请输入语言名称/编码/描述" clearable @keyup.enter="handleSearch"
                style="width: 220px;" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <el-icon>
                  <Search />
                </el-icon>
                搜索
              </el-button>
              <el-button @click="handleReset">
                <el-icon>
                  <Refresh />
                </el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" v-for="item in tableData" :key="item.id">
          <el-card :key="item.id" class="language-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <div class="language-info">
                  <span v-if="item.color" class="color-indicator" :style="{ backgroundColor: item.color }"></span>
                  <span class="language-name">{{ item.name }}</span>
                  <el-tag size="small" type="info">{{ item.language_code }}</el-tag>
                </div>
                <div class="card-actions">
                  <el-dropdown trigger="click">
                    <el-button type="text" size="small">
                      <el-icon>
                        <MoreFilled />
                      </el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="handleEdit(item)">
                          <el-icon>
                            <Edit />
                          </el-icon>
                          编辑
                        </el-dropdown-item>
                        <el-dropdown-item @click="handleDockerfile(item)">
                          <el-icon>
                            <Document />
                          </el-icon>
                          Dockerfile
                        </el-dropdown-item>
                        <el-dropdown-item @click="handlePipeline(item)">
                          <el-icon>
                            <Setting />
                          </el-icon>
                          流水线
                        </el-dropdown-item>
                        <el-dropdown-item @click="handleDelete(item)" divided>
                          <el-icon>
                            <Delete />
                          </el-icon>
                          删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </template>

            <!-- <div class="card-content"> -->
            <el-descriptions border :column="2" style="width: 100%;">
              <el-descriptions-item label="ID">
                <el-tag size="small" type="info">{{ item.id }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="编码">
                <span size="small" type="info">{{ item.language_code }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="排序">
                <el-tag size="small" type="info">{{ item.sort_order }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="描述">
                <span>{{ item.description }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                <span>{{ formatDate(item.created_at) }}</span>
              </el-descriptions-item>
            </el-descriptions>
            <!-- </div> -->

            <template #footer>
              <div class="card-footer">
                <el-button size="small" type="primary" @click="handleEdit(item)">
                  <el-icon>
                    <Edit />
                  </el-icon>&nbsp;编辑
                </el-button>
                <el-button size="small" type="info" @click="handleDockerfile(item)">
                  <el-icon>
                    <Document />
                  </el-icon>&nbsp;Dockerfile
                </el-button>
                <el-button size="small" type="warning" @click="handlePipeline(item)">
                  <el-icon>
                    <Setting />
                  </el-icon>&nbsp;流水线
                </el-button>
                <el-button size="small" type="danger" @click="handleDelete(item)">
                  <el-icon>
                    <Delete />
                  </el-icon>&nbsp;删除
                </el-button>
              </div>
            </template>
          </el-card>
        </el-col>
      </el-row>

      <!-- 空状态 -->
      <div v-if="!loading && tableData.length === 0" class="empty-state">
        <el-empty description="暂无开发语言数据">
          <el-button type="primary" @click="handleCreate">
            <el-icon>
              <Plus />
            </el-icon>
            新增语言
          </el-button>
        </el-empty>
      </div>
      <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.pageSize"
        :total="pagination.total" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" style="margin-top: 20px;" />
    </el-card>

    <!-- 分页 -->
    <!-- <div class="pagination-container"> -->

    <!-- </div> -->

    <!-- 表单对话框 -->
    <LanguageFormDialog v-model:visible="formDialogVisible" :language="currentLanguage" @success="handleFormSuccess" />

    <!-- Dockerfile编辑器 -->
    <DockerfileEditor v-model:visible="dockerfileEditorVisible" :language="currentLanguage"
      @saved="handleDockerfileSaved" />

    <!-- Pipeline编辑器 -->
    <PipelineEditorEnhanced v-model:visible="pipelineEditorVisible" :source-data="currentLanguage"
      source-type="language" @saved="handlePipelineSaved" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, Edit, Document, Setting, Delete, MoreFilled } from '@element-plus/icons-vue'
import LanguageFormDialog from './components/LanguageFormDialog.vue'
import DockerfileEditor from './components/DockerfileEditor.vue'
import PipelineEditorEnhanced from './components/PipelineEditorEnhanced.vue'
import { languageApi } from '../../../api/modules/cmdb'
import type { DevLanguage, DevLanguageListQuery } from '../../../api/modules/cmdb'
import { handleAPIResponse } from '../../../utils/response'

// 状态定义
const loading = ref(false)
const formDialogVisible = ref(false)
const dockerfileEditorVisible = ref(false)
const pipelineEditorVisible = ref(false)
const currentLanguage = ref<DevLanguage | null>(null)

// 搜索表单
const searchForm = reactive<DevLanguageListQuery>({
  search: '',
  page: 1,
  page_size: 20
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 表格数据
const tableData = ref<DevLanguage[]>([])

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 加载数据
const loadData = async () => {
  loading.value = true

  try {
    const params: DevLanguageListQuery = {
      page: pagination.page,
      page_size: pagination.pageSize
    }

    // 添加搜索条件
    if (searchForm.search) {
      params.search = searchForm.search
    }

    const response = await languageApi.getLanguages(params)
    const result = handleAPIResponse(response) as { items: DevLanguage[], total: number }

    if (result && result.items) {
      tableData.value = result.items
      pagination.total = result.total || 0
    } else {
      tableData.value = []
      pagination.total = 0
    }

  } catch (error) {
    console.error('加载开发语言列表失败:', error)
    ElMessage.error('加载开发语言列表失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置搜索
const handleReset = () => {
  searchForm.search = ''
  pagination.page = 1
  loadData()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.page = 1
  loadData()
}

// 当前页变化
const handleCurrentChange = (val: number) => {
  pagination.page = val
  loadData()
}

// 新增
const handleCreate = () => {
  currentLanguage.value = null
  formDialogVisible.value = true
}

// 编辑
const handleEdit = (row: DevLanguage) => {
  currentLanguage.value = { ...row }
  formDialogVisible.value = true
}

// 删除
const handleDelete = (row: DevLanguage) => {
  ElMessageBox.confirm(
    `确定要删除开发语言 "${row.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await languageApi.deleteLanguage(row.id)
      handleAPIResponse(response)

      ElMessage.success('删除成功')
      loadData()
    } catch (error) {
      console.error('删除开发语言失败:', error)
      ElMessage.error('删除开发语言失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// Dockerfile编辑
const handleDockerfile = (row: DevLanguage) => {
  currentLanguage.value = { ...row }
  dockerfileEditorVisible.value = true
}

// Pipeline编辑
const handlePipeline = (row: DevLanguage) => {
  currentLanguage.value = { ...row }
  pipelineEditorVisible.value = true
}

// 表单提交成功
const handleFormSuccess = () => {
  loadData()
}

// Dockerfile保存成功
const handleDockerfileSaved = () => {
  ElMessage.success('Dockerfile模板保存成功')
  loadData()
}

// Pipeline保存成功
const handlePipelineSaved = () => {
  ElMessage.success('Pipeline模板保存成功')
  pipelineEditorVisible.value = false
  loadData()
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.language-container {
  /* height: 100%; */
  display: flex;
  flex-direction: column;
  /* padding: 20px; */
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-title h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-title p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-bar {
  background: #fff;
  /* padding: 20px 20px 0 20px; */
  border-radius: 8px;
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
  /* margin-bottom: 20px; */
}

.cards-container {
  flex: 1;
  /* overflow-y: auto; */
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  padding: 20px;
}

.language-card {
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
}

.language-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.language-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.language-name {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid #ddd;
  flex-shrink: 0;
}

.card-actions {
  display: flex;
  align-items: center;
}

.card-content {
  padding: 16px 0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f5f7fa;
}

.info-row:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
  min-width: 80px;
}

.value {
  color: #303133;
  font-size: 14px;
  text-align: right;
  word-break: break-all;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-footer {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 12px 0 0 0;
  width: 100%;
}

.card-footer .el-button {
  min-width: 90px;
  flex: 1 1 40%;
  border-radius: 6px;
  font-size: 14px;
  font-weight: normal;
  padding: 0 10px;
  white-space: nowrap;
  margin: 0;
  transition: box-shadow 0.15s;
}

@media (max-width: 900px) {
  .card-footer {
    gap: 8px;
  }

  .card-footer .el-button {
    min-width: 120px;
    flex: 1 1 100%;
    margin-bottom: 4px;
  }
}

@media (max-width: 600px) {
  .card-footer {
    flex-direction: column;
    gap: 6px;
    padding: 8px 0 0 0;
  }

  .card-footer .el-button {
    min-width: 100%;
    margin-bottom: 0;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 40px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  background: #fff;
  border-radius: 8px;
  margin-top: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: 1fr;
    padding: 10px;
    gap: 15px;
  }

  .card-footer .el-button-group {
    flex-direction: column;
    gap: 4px;
  }

  .card-footer .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }

  .language-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

.el-form /deep/ .el-form-item {
  margin-bottom: 0 !important;
  margin-right: 10px !important;
}
</style>