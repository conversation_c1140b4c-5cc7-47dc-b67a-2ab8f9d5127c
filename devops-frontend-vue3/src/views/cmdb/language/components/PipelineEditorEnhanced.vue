<template>
  <el-drawer
    v-model="visible"
    :title="title"
    size="calc(100% - 240px)"
    direction="rtl"
    destroy-on-close
    class="pipeline-drawer"
  >
    <div v-if="sourceData" class="pipeline-container" v-loading="loading">
      <div class="pipeline-header">
        <el-alert
          :title="getAlertTitle()"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p v-if="sourceType === 'language'">正在配置语言 <strong>{{ sourceData.name }}</strong> 的流水线模板</p>
            <p v-else-if="sourceType === 'microapp'">正在配置应用 <strong>{{ sourceData.name }}</strong> 的流水线</p>
            <p v-else>正在配置 <strong>{{ sourceData.name }}</strong> 的流水线</p>
            <p v-if="currentPipelineId">流水线ID: {{ currentPipelineId }}</p>
            <p v-else-if="inheritedPipelineInfo">
              <el-tag type="warning" size="small">继承自{{ inheritedPipelineInfo.source }}</el-tag>
              {{ inheritedPipelineInfo.description }}
            </p>
            <p v-else>暂未创建流水线配置</p>
          </template>
        </el-alert>
      </div>
      
      <!-- 应用Pipeline继承配置 - 仅在没有自定义Pipeline时显示 -->
      <div v-if="sourceType === 'microapp' && (pipelineMode === 'inherit' || (!currentPipelineId || currentPipelineId === 0))" class="inheritance-config">
        <el-card shadow="never" class="mb-4">
          <template #header>
            <div class="card-header">
              <span>流水线配置模式</span>
            </div>
          </template>
          
          <el-radio-group v-model="pipelineMode" @change="handlePipelineModeChange">
            <!-- <el-space direction="vertical" size="large"> -->
              <el-radio value="inherit">
                <div class="radio-content">
                  <div class="radio-title">继承开发语言配置</div>
                  <div class="radio-description">
                    使用 <strong>{{ languageName }}</strong> 语言的默认流水线模板
                    <el-tag v-if="languagePipelineInfo" type="success" size="small" class="ml-2">
                      {{ languagePipelineInfo.name }}
                    </el-tag>
                  </div>
                </div>
              </el-radio>
              
              <el-radio value="custom">
                <div class="radio-content">
                  <div class="radio-title">自定义应用流水线</div>
                  <div class="radio-description">
                    基于语言模板创建应用专属的流水线配置，可独立调整构建、测试、部署流程
                  </div>
                </div>
              </el-radio>
            <!-- </el-space> -->
          </el-radio-group>
        </el-card>
      </div>
      
      <!-- 继承模式显示 -->
      <div v-if="sourceType === 'microapp' && pipelineMode === 'inherit'" class="inherited-pipeline">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>继承的流水线配置</span> <el-button type="info" key="plain" text bg size="small" @click="showYamlPreview">YAML预览</el-button>
            </div>
          </template>
          
          <div v-if="inheritedPipelineConfig" class="inherited-content">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="流水线名称">
                {{ inheritedPipelineConfig.name || '未命名' }}
              </el-descriptions-item>
              <el-descriptions-item label="来源">
                {{ inheritedPipelineInfo?.source || '开发语言' }}
              </el-descriptions-item>
              <el-descriptions-item label="描述" :span="2">
                {{ inheritedPipelineConfig.description || '无描述' }}
              </el-descriptions-item>
            </el-descriptions>
            
            <div class="inherited-stats mt-4">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-statistic title="参数数量" :value="inheritedPipelineConfig.parameters?.length || 0" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="工作空间" :value="inheritedPipelineConfig.workspaces?.length || 0" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="任务数量" :value="inheritedPipelineConfig.tasks?.length || 0" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="触发条件" :value="inheritedPipelineConfig.triggers?.length || 0" />
                </el-col>
              </el-row>
            </div>
            
            <!-- 任务流程预览优化 -->
            <div v-if="inheritedPipelineConfig.tasks?.length > 0" class="task-flow-preview mt-4">
              <h4>任务流程预览</h4>
              <div class="task-flow-scroll">
                <template v-for="(task, index) in inheritedPipelineConfig.tasks" :key="'task-tpl-' + index">
                  <div class="task-card" :class="`task-type-${task.type}`">
                    <div class="task-header">
                      <el-tag :type="getTaskTypeColor(task.type)" size="small">
                        {{ getTaskTypeLabel(task.type) }}
                      </el-tag>
                      <span class="task-name">{{ task.name || `任务${index + 1}` }}</span>
                    </div>
                    <div class="task-meta">
                      <span v-if="task.image" class="task-image">
                        <el-icon><Box /></el-icon> {{ task.image }}
                      </span>
                      <span v-if="task.runAfter?.length" class="task-deps">
                        <el-icon><Connection /></el-icon> 依赖: {{ task.runAfter.join(', ') }}
                      </span>
                    </div>
                  </div>
                  <div v-if="index < inheritedPipelineConfig.tasks.length - 1" class="task-arrow">
                    <el-icon><ArrowRight /></el-icon>
                  </div>
                </template>
              </div>
            </div>
            <!-- YAML 预览 -->
            <!-- <el-divider content-position="left">流水线 YAML 预览</el-divider>
            <el-collapse v-model="yamlCollapse">
              <el-collapse-item name="yaml">
                <template #title>
                  <el-icon><Document /></el-icon> YAML 配置
                  <el-button size="small" type="primary" @click.stop="copyYaml('inherit')" style="margin-left: 12px">复制</el-button>
                </template>
                <pre class="yaml-preview">{{ generateYamlFromConfig(inheritedPipelineConfig) }}</pre>
              </el-collapse-item>
            </el-collapse> -->
          </div>
          
          <el-empty v-else description="未找到可继承的流水线配置">
            <el-button type="primary" @click="switchToCustomMode">
              创建自定义配置
            </el-button>
          </el-empty>
        </el-card>
      </div>

      <el-drawer v-model="yamlPreviewVisible" title="YAML 预览" size="90%" direction="rtl" destroy-on-close>
        <el-icon><Document /></el-icon> YAML配置预览
        <el-button size="small" type="info" bg key="plain" text @click.stop="copyYaml('inherit')" style="margin-left: 12px">复制</el-button>
        <pre class="yaml-preview">{{ generateYamlFromConfig(inheritedPipelineConfig) }}</pre>
      </el-drawer>
      
      <!-- 无Pipeline配置时的空状态 -->
      <div v-if="!currentPipelineId && !showPipelineDrawer && !isCreatingTemplate && (sourceType === 'language' || (sourceType === 'microapp' && pipelineMode === 'custom'))">
        <el-card shadow="never">
          <el-empty :description="sourceType === 'language' ? '暂无流水线模板配置' : '暂无流水线配置'">
            <el-button type="primary" @click="createPipeline">
              {{ sourceType === 'language' ? '创建流水线模板' : '创建流水线' }}
            </el-button>
          </el-empty>
        </el-card>
      </div>
      
      <!-- 流水线配置表单 -->
      <div v-if="(sourceType === 'language' && (currentPipelineId > 0 || isCreatingTemplate)) || (sourceType === 'microapp' && (pipelineMode === 'custom' || (pipelineMode !== 'inherit' && currentPipelineId > 0)))" class="pipeline-config">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基础配置 -->
          <el-tab-pane label="基础配置" name="basic">
            <!-- 自定义Pipeline提示 -->
            <el-alert 
              v-if="sourceType === 'microapp' && currentPipelineId > 0"
              :title="`正在配置应用 ${sourceData?.name || 'Unknown'} 的流水线`"
              type="info"
              :description="`流水线ID: ${currentPipelineId}`"
              show-icon
              :closable="false"
              class="mb-4"
            />
            
            <el-form :model="pipelineConfig" label-width="120px" class="config-form">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="流水线名称">
                    <el-input 
                      v-model="pipelineConfig.name"
                      :placeholder="sourceType === 'language' ? '请输入流水线模板名称' : '请输入流水线名称'"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="流水线类型">
                    <el-select v-model="pipelineConfig.pipeline_type" placeholder="选择类型" style="width: 100%">
                      <el-option label="完整流水线" value="full" />
                      <el-option label="构建流水线" value="build" />
                      <el-option label="测试流水线" value="test" />
                      <el-option label="部署流水线" value="deploy" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="描述">
                <el-input 
                  v-model="pipelineConfig.description" 
                  type="textarea" 
                  :rows="3"
                  :placeholder="sourceType === 'language' ? '请输入流水线模板描述' : '请输入流水线描述'"
                />
              </el-form-item>
              
              <el-form-item label="触发条件">
                <el-checkbox-group v-model="pipelineConfig.triggers">
                  <el-checkbox label="push" value="push">代码推送</el-checkbox>
                  <el-checkbox label="merge_request" value="merge_request">合并请求</el-checkbox>
                  <el-checkbox label="tag" value="tag">标签创建</el-checkbox>
                  <el-checkbox label="manual" value="manual">手动触发</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <!-- 流水线模式切换 - 仅应用级别显示 -->
              <div v-if="sourceType === 'microapp'" class="template-actions">
                <el-divider content-position="left">流水线模式操作</el-divider>
                
                <!-- 自定义Pipeline的操作 -->
                <div v-if="currentPipelineId > 0">
                  <el-button 
                    type="warning" 
                    size="small" 
                    @click="switchToInheritMode"
                    style="margin-bottom: 10px;"
                  >
                    <el-icon><Refresh /></el-icon>
                    切换到继承模式
                  </el-button>
                  <el-text type="info" size="small" class="ml-2">
                    切换后将删除当前自定义配置，使用语言默认模板
                  </el-text>
                </div>
                
                <!-- 语言模板初始化操作 -->
                <div v-if="languagePipelineInfo">
                  <el-button 
                    type="success" 
                    size="small" 
                    @click="initFromLanguageTemplate"
                    :disabled="hasInitialized"
                  >
                    <el-icon><DocumentCopy /></el-icon>
                    从语言模板初始化
                  </el-button>
                  <el-button 
                    type="warning" 
                    size="small" 
                    @click="resetToLanguageTemplate"
                    v-if="hasInitialized"
                  >
                    <el-icon><Refresh /></el-icon>
                    重置为语言模板
                  </el-button>
                </div>
              </div>
            </el-form>
          </el-tab-pane>
          
          <!-- 参数配置 -->
          <el-tab-pane label="参数配置" name="params">
            <div class="params-section">
              <div class="section-header">
                <h4>流水线参数</h4>
                <el-button type="primary" size="small" @click="addParameter">
                  <el-icon><Plus /></el-icon>
                  添加参数
                </el-button>
              </div>
              
              <el-table :data="pipelineConfig.parameters" style="width: 100%">
                <el-table-column prop="name" label="参数名称" width="200">
                  <template #default="{ row }">
                    <el-input v-model="row.name" placeholder="参数名称" size="small" />
                  </template>
                </el-table-column>
                <el-table-column prop="type" label="参数类型" width="150">
                  <template #default="{ row }">
                    <el-select v-model="row.type" placeholder="类型" size="small" style="width: 100%">
                      <el-option label="字符串" value="string" />
                      <el-option label="数字" value="number" />
                      <el-option label="布尔值" value="boolean" />
                      <el-option label="数组" value="array" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="default_value" label="默认值" width="200">
                  <template #default="{ row }">
                    <el-input v-model="row.default_value" placeholder="默认值" size="small" />
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述">
                  <template #default="{ row }">
                    <el-input v-model="row.description" placeholder="参数描述" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template #default="{ $index }">
                    <el-button 
                      type="danger" 
                      size="small" 
                      @click="removeParameter($index)"
                      :icon="Delete"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <!-- 工作空间配置 -->
          <el-tab-pane label="工作空间" name="workspaces">
            <div class="workspaces-section">
              <div class="section-header">
                <h4>工作空间配置</h4>
                <div class="header-actions">
                  <el-button 
                    v-if="pipelineConfig.workspaces.length === 0" 
                    type="success" 
                    size="small" 
                    @click="createDefaultWorkspaces"
                  >
                    <el-icon><Refresh /></el-icon>
                    创建默认工作空间
                  </el-button>
                  <el-button type="primary" size="small" @click="addWorkspace">
                    <el-icon><Plus /></el-icon>
                    添加工作空间
                  </el-button>
                </div>
              </div>
              
              <el-table :data="pipelineConfig.workspaces" style="width: 100%">
                <el-table-column prop="name" label="工作空间名称" width="200">
                  <template #default="{ row }">
                    <el-input v-model="row.name" placeholder="工作空间名称" size="small" />
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述">
                  <template #default="{ row }">
                    <el-input v-model="row.description" placeholder="工作空间描述" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="使用情况" width="150">
                  <template #default="{ row }">
                    <div class="workspace-usage">
                      <el-tag 
                        v-if="getWorkspaceUsageInfo(row.name).isUsed" 
                        type="success" 
                        size="small"
                      >
                        已使用 {{ getWorkspaceUsageInfo(row.name).totalUsage }} 次
                      </el-tag>
                      <el-tag v-else type="info" size="small">未使用</el-tag>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="optional" label="可选" width="80">
                  <template #default="{ row }">
                    <el-switch v-model="row.optional" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template #default="{ $index }">
                    <el-button 
                      type="danger" 
                      size="small" 
                      @click="removeWorkspace($index)"
                      :icon="Delete"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <!-- 任务配置 -->
          <el-tab-pane label="任务配置" name="tasks">
            <div class="tasks-section">
              <div class="section-header">
                <h4>流水线任务</h4>
                <el-button type="primary" size="small" @click="addTask">
                  <el-icon><Plus /></el-icon>
                  添加任务
                </el-button>
              </div>
              
              <div class="tasks-list">
                <div 
                  v-for="(task, index) in pipelineConfig.tasks" 
                  :key="index"
                  class="task-item"
                >
                  <el-card shadow="hover">
                    <template #header>
                      <div class="task-header">
                        <span>任务 {{ index + 1 }}: {{ task.name || '未命名任务' }}</span>
                        <el-button 
                          type="danger" 
                          size="small" 
                          @click="removeTask(index)"
                          :icon="Delete"
                        />
                      </div>
                    </template>
                    
                    <el-form :model="task" label-width="100px" size="small">
                      <el-row :gutter="20">
                        <el-col :span="12">
                          <el-form-item label="任务名称">
                            <el-input v-model="task.name" placeholder="任务名称" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="任务类型">
                            <el-select v-model="task.type" placeholder="选择任务类型" style="width: 100%">
                              <el-option label="代码克隆" value="git-clone" />
                              <el-option label="构建应用" value="build-app" />
                              <el-option label="构建镜像" value="build-image" />
                              <el-option label="运行测试" value="run-tests" />
                              <el-option label="部署应用" value="deploy-app" />
                              <el-option label="自定义脚本" value="custom-script" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      
                      <el-form-item label="镜像">
                        <el-input v-model="task.image" placeholder="容器镜像" />
                      </el-form-item>
                      
                      <el-form-item label="执行脚本">
                        <el-input 
                          v-model="task.script" 
                          type="textarea" 
                          :rows="4"
                          placeholder="请输入执行脚本"
                        />
                      </el-form-item>
                      
                      <el-form-item label="依赖任务">
                        <el-select 
                          v-model="task.runAfter" 
                          multiple 
                          placeholder="选择依赖的任务"
                          style="width: 100%"
                        >
                          <el-option 
                            v-for="(otherTask, otherIndex) in pipelineConfig.tasks"
                            :key="otherIndex"
                            :label="otherTask.name || `任务${otherIndex + 1}`"
                            :value="otherTask.name"
                            :disabled="otherIndex >= index"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="工作空间">
                        <div class="task-workspaces">
                          <div v-if="task.workspaces && task.workspaces.length > 0" class="workspace-list">
                            <div v-for="(ws, wsIndex) in task.workspaces" :key="`ws-${index}-${wsIndex}`" class="workspace-item">
                              <el-row :gutter="12" align="middle">
                                <el-col :span="8">
                                  <el-input 
                                    v-model="ws.name" 
                                    placeholder="工作空间名称" 
                                    size="small"
                                  />
                                </el-col>
                                <el-col :span="12">
                                  <el-tooltip 
                                    v-if="ws.workspace && getWorkspaceUsageInfo(ws.workspace).isUsed"
                                    :content="`此工作空间已在以下任务中使用: ${getWorkspaceUsageInfo(ws.workspace).usedInTasks.join(', ')}`"
                                    placement="top"
                                  >
                                    <el-select 
                                      v-model="ws.workspace" 
                                      placeholder="选择流水线工作空间" 
                                      style="width: 100%"
                                      size="small"
                                    >
                                      <el-option 
                                        v-for="pws in pipelineConfig.workspaces" 
                                        :key="pws.name" 
                                        :label="getWorkspaceOptionLabel(pws, task, wsIndex)"
                                        :value="pws.name"
                                        :disabled="isWorkspaceUsedInTask(task, pws.name, wsIndex)"
                                      />
                                    </el-select>
                                  </el-tooltip>
                                  <el-select 
                                    v-else
                                    v-model="ws.workspace" 
                                    placeholder="选择流水线工作空间" 
                                    style="width: 100%"
                                    size="small"
                                  >
                                    <el-option 
                                      v-for="pws in pipelineConfig.workspaces" 
                                      :key="pws.name" 
                                      :label="getWorkspaceOptionLabel(pws, task, wsIndex)"
                                      :value="pws.name"
                                      :disabled="isWorkspaceUsedInTask(task, pws.name, wsIndex)"
                                    />
                                  </el-select>
                                </el-col>
                                <el-col :span="4">
                                  <el-button 
                                    type="danger" 
                                    size="small" 
                                    @click="removeTaskWorkspace(index, wsIndex)"
                                    :icon="Delete"
                                  />
                                </el-col>
                              </el-row>
                            </div>
                          </div>
                          
                          <div class="workspace-actions">
                            <el-button 
                              type="primary" 
                              size="small" 
                              @click="addTaskWorkspace(index)"
                              :icon="Plus"
                            >
                              添加工作空间绑定
                            </el-button>
                            <el-text v-if="pipelineConfig.workspaces.length === 0" type="warning" size="small">
                              请先在工作空间配置中添加流水线工作空间
                            </el-text>
                          </div>
                        </div>
                      </el-form-item>
                    </el-form>
                  </el-card>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- YAML预览 -->
          <el-tab-pane label="YAML预览" name="yaml">
            
            <el-card shadow="never">
            <div class="preview-header">
              <h4>生成的Pipeline YAML</h4>
              <div class="preview-actions">
                <el-button size="small" @click="refreshYaml">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                <el-button type="primary" size="small" @click="copyYaml('custom')">
                  <el-icon><DocumentCopy /></el-icon>
                  复制YAML
                </el-button>
              </div>
            </div>
            <div class="yaml-stats">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-statistic title="参数数量" :value="pipelineConfig.parameters?.length || 0" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="工作空间" :value="pipelineConfig.workspaces?.length || 0" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="任务数量" :value="pipelineConfig.tasks?.length || 0" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="触发条件" :value="pipelineConfig.triggers?.length || 0" />
                </el-col>
              </el-row>
              
              <!-- 任务流程预览优化 -->
              <div v-if="pipelineConfig.tasks?.length > 0" class="task-flow-preview mt-4">
                <h4>任务流程预览</h4>
                <div class="task-flow-scroll">
                  <template v-for="(task, index) in pipelineConfig.tasks" :key="'task-tpl2-' + index">
                    <div class="task-card" :class="`task-type-${task.type}`">
                      <div class="task-header">
                        <el-tag :type="getTaskTypeColor(task.type)" size="small">
                          {{ getTaskTypeLabel(task.type) }}
                        </el-tag>
                        <span class="task-name">{{ task.name || `任务${index + 1}` }}</span>
                      </div>
                      <div class="task-meta">
                        <span v-if="task.image" class="task-image">
                          <el-icon><Box /></el-icon> {{ task.image }}
                        </span>
                        <span v-if="task.runAfter?.length" class="task-deps">
                          <el-icon><Connection /></el-icon> 依赖: {{ task.runAfter.join(', ') }}
                        </span>
                      </div>
                    </div>
                    <div v-if="index < pipelineConfig.tasks.length - 1" class="task-arrow">
                      <el-icon><ArrowRight /></el-icon>
                    </div>
                  </template>
                </div>
              </div>
            </div>
            </el-card>
            <div class="yaml-preview">
              <pre class="yaml-content">{{ yamlContent }}</pre>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="currentPipelineId && pipelineMode !== 'inherit'" type="danger" @click="deletePipeline">删除流水线</el-button>
        <el-button 
          v-if="isCreatingTemplate" 
          @click="cancelCreateTemplate"
          :loading="saving"
        >
          取消创建
        </el-button>
        <el-button 
          v-else
          type="warning"
          @click="resetConfig" 
          :loading="saving"
        >
          重置配置
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSave" 
          :loading="saving"
          :disabled="!hasChanges"
        >
          {{ isCreatingTemplate ? '创建模板' : '保存配置' }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document,Plus, Delete, DocumentCopy, Refresh, Box, Connection, ArrowRight } from '@element-plus/icons-vue'
import { pipelineApi, languageApi } from '../../../../api/modules/cmdb'
import { handleAPIResponse } from '../../../../utils/response'

// 定义类型
interface DevLanguage {
  id: number
  name: string
  language_code?: string
  description?: string
  dockerfile_template?: string
  pipeline_template?: any
  icon?: string
  color?: string
  sort_order: number
  created_at: string
  updated_at: string
}

interface PipelineParameter {
  name: string
  type: string
  default_value: string
  description: string
}

interface PipelineWorkspace {
  name: string
  description: string
  optional: boolean
}

interface PipelineTask {
  name: string
  type: string
  image: string
  script: string
  runAfter: string[]
  workspaces: {
    name: string
    workspace: string
  }[]
}

interface PipelineConfig {
  name: string
  pipeline_type: string
  description: string
  triggers: string[]
  parameters: PipelineParameter[]
  workspaces: PipelineWorkspace[]
  tasks: PipelineTask[]
}

// Props
interface Props {
  visible: boolean
  sourceData?: any | null
  sourceType: string
}

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'saved'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  sourceData: null,
  sourceType: 'language',
})

const emit = defineEmits<Emits>()

// 状态定义
const loading = ref(false)
const saving = ref(false)
const hasChanges = ref(false)
const activeTab = ref('basic')
const yamlContent = ref('')
const showPipelineDrawer = ref(false)
const pipelineMode = ref('inherit')
const languagePipelineInfo = ref<any | null>(null)
const inheritedPipelineConfig = ref<PipelineConfig | null>(null)
const inheritedPipelineInfo = ref<{ source: string; description: string } | null>(null)
const hasInitialized = ref(false)
const yamlPreviewVisible = ref(false)
const isCreatingTemplate = ref(false)

// 流水线配置
const pipelineConfig = ref<PipelineConfig>({
  name: '',
  pipeline_type: 'full',
  description: '',
  triggers: ['push'],
  parameters: [
    {
      name: 'git-url',
      type: 'string',
      default_value: '',
      description: 'Git repository URL'
    },
    {
      name: 'git-revision',
      type: 'string', 
      default_value: 'main',
      description: 'Git revision'
    },
    {
      name: 'image-name',
      type: 'string',
      default_value: '',
      description: 'Container image name'
    },
    {
      name: 'image-tag',
      type: 'string',
      default_value: 'latest',
      description: 'Container image tag'
    }
  ],
  workspaces: [
    {
      name: 'shared-data',
      description: 'Shared workspace for source code',
      optional: false
    },
    {
      name: 'docker-credentials',
      description: 'Docker registry credentials',
      optional: false
    }
  ],
  tasks: [
    {
      name: 'fetch-source',
      type: 'git-clone',
      image: '',
      script: '',
      runAfter: [],
      workspaces: []
    }
  ]
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const title = computed(() => {
  return props.sourceData ? `流水线配置 - ${props.sourceData.name}` : '流水线配置'
})

const currentPipelineId = computed(() => {
  if (props.sourceType === 'microapp') {
    return props.sourceData?.pipeline_id || 0
  }
  return props.sourceData?.pipeline_id || 0
})

const languageName = computed(() => {
  if (props.sourceType === 'microapp') {
    return props.sourceData?.language_code || 'Unknown'
  }
  return props.sourceData?.name || 'Unknown'
})

const deletePipeline = async () => {
  await pipelineApi.deletePipeline(currentPipelineId.value)
  ElMessage.success('流水线删除成功')
  hasChanges.value = false
  showPipelineDrawer.value = false
}

const showYamlPreview = () => {
  yamlPreviewVisible.value = true
}

// 监听配置变化
watch(pipelineConfig, () => {
  hasChanges.value = true
  // 自动更新YAML预览
  if (activeTab.value === 'yaml') {
    yamlContent.value = generateYaml()
  }
}, { deep: true })

// 监听标签页变化，切换到YAML页面时更新内容
watch(activeTab, (newTab) => {
  if (newTab === 'yaml') {
    yamlContent.value = generateYaml()
  }
})

// 取消创建模板
const cancelCreateTemplate = () => {
  isCreatingTemplate.value = false
  hasChanges.value = false
  
  // 重置配置为默认值
  pipelineConfig.value = {
    name: '',
    pipeline_type: 'full',
    description: '',
    triggers: ['push'],
    parameters: [
      {
        name: 'git-url',
        type: 'string',
        default_value: '',
        description: 'Git repository URL'
      },
      {
        name: 'git-revision',
        type: 'string', 
        default_value: 'main',
        description: 'Git revision'
      },
      {
        name: 'image-name',
        type: 'string',
        default_value: '',
        description: 'Container image name'
      },
      {
        name: 'image-tag',
        type: 'string',
        default_value: 'latest',
        description: 'Container image tag'
      }
    ],
    workspaces: [
      {
        name: 'shared-data',
        description: 'Shared workspace for source code',
        optional: false
      },
      {
        name: 'docker-credentials',
        description: 'Docker registry credentials',
        optional: false
      }
    ],
    tasks: [
      {
        name: 'fetch-source',
        type: 'git-clone',
        image: '',
        script: '',
        runAfter: [],
        workspaces: []
      }
    ]
  }
  
  ElMessage.info('已取消创建模板')
}

// 初始化语言模板
const initializeLanguageTemplate = async () => {
  if (!props.sourceData) return
  
  // 重置配置为默认值
  pipelineConfig.value.name = `${props.sourceData.name.toLowerCase()}-pipeline-template`
  pipelineConfig.value.description = `${props.sourceData.name} 语言流水线模板`
  pipelineConfig.value.pipeline_type = 'full'
  pipelineConfig.value.triggers = ['push']
  
  // 创建默认工作空间
  pipelineConfig.value.workspaces = [
    {
      name: 'shared-data',
      description: '共享数据存储，用于在任务间传递文件',
      optional: false
    },
    {
      name: 'docker-credentials', 
      description: 'Docker镜像仓库凭证',
      optional: true
    }
  ]
  
  // 添加默认任务
  const defaultTasks = getDefaultTasksForLanguage(props.sourceData as DevLanguage)
  pipelineConfig.value.tasks = defaultTasks
  
  // 更新YAML预览
  yamlContent.value = generateYaml()
}

// createPipeline
const createPipeline = async () => {
  if (props.sourceType === 'language') {
    // 语言模式：设置创建状态并初始化默认配置
    isCreatingTemplate.value = true
    
    // 初始化语言模板的默认配置
    await initializeLanguageTemplate()
    
    // 标记有变更
    hasChanges.value = true
    
    ElMessage.success('已初始化流水线模板配置，请完善后保存')
  } else {
    showPipelineDrawer.value = true
  }
}

// 初始化配置
const initializeConfig = async () => {
  if (!props.sourceData) {
    console.warn('sourceData is null, skipping initialization')
    return
  }
  
  try {
    loading.value = true
    
    if (props.sourceType === 'language') {
      // 语言模式：语言级别的Pipeline是最上级模板，不存在继承
      if (props.sourceData?.pipeline_id === 0 || !props.sourceData?.pipeline_id) {
        // 初始化空配置
        pipelineConfig.value.name = `${props.sourceData.name.toLowerCase()}-pipeline-template`
        pipelineConfig.value.description = `${props.sourceData.name} 语言流水线模板`
        pipelineConfig.value.pipeline_type = 'full'
        pipelineConfig.value.triggers = ['push']
        return
      }
      
      try {
        // 加载现有的语言Pipeline配置
        const pipeline = await pipelineApi.getPipeline(props.sourceData.pipeline_id)
        const result = handleAPIResponse(pipeline) as any
        pipelineConfig.value = result.pipeline_config || result
        hasChanges.value = false
        yamlContent.value = generateYaml()
      } catch (error) {
        console.error('加载语言Pipeline失败:', error)
        ElMessage.error('加载语言Pipeline配置失败')
      }
    } else if (props.sourceType === 'microapp') {
      // 应用模式：检查应用是否有自定义Pipeline
      if (currentPipelineId.value && currentPipelineId.value > 0) {
        // 有自定义Pipeline，加载配置
        try {
          const pipeline = await pipelineApi.getPipeline(currentPipelineId.value)
          const result = handleAPIResponse(pipeline) as any
          pipelineConfig.value = result.pipeline_config || result
          pipelineMode.value = 'custom'
          hasInitialized.value = true
        } catch (error) {
          console.error('加载应用Pipeline失败:', error)
          ElMessage.error('加载应用Pipeline配置失败')
          // 如果加载失败，切换到继承模式
          pipelineMode.value = 'inherit'
          await loadLanguagePipeline()
        }
      } else {
        // 没有自定义Pipeline，默认继承模式
        pipelineMode.value = 'inherit'
        await loadLanguagePipeline()
      }
    }
  } catch (error) {
    console.error('初始化配置失败:', error)
    ElMessage.error('初始化配置失败')
  } finally {
    loading.value = false
  }
}

// 获取语言默认任务
const getDefaultTasksForLanguage = (language: DevLanguage) => {
  const baseImage = getBaseImageForLanguage(language.name)
  const buildCommand = getBuildCommandForLanguage(language.name)
  
  return [
    {
      name: 'fetch-source',
      type: 'git-clone',
      image: 'gcr.io/tekton-releases/github.com/tektoncd/pipeline/cmd/git-init:latest',
      script: '# git-clone任务使用ClusterTask，无需自定义脚本',
      runAfter: [],
      workspaces: [{ name: 'output', workspace: 'shared-data' }]
    },
    {
      name: 'build-app',
      type: 'build-app',
      image: baseImage,
      script: `#!/bin/sh
set -e
echo "开始构建 ${language.name} 应用..."
${buildCommand}
echo "构建完成"`,
      runAfter: ['fetch-source'],
      workspaces: [{ name: 'source', workspace: 'shared-data' }]
    },
    {
      name: 'build-image',
      type: 'build-image',
      image: 'quay.io/buildah/stable:latest',
      script: '# build-image任务使用buildah ClusterTask，无需自定义脚本',
      runAfter: ['build-app'],
      workspaces: [
        { name: 'source', workspace: 'shared-data' },
        { name: 'dockerconfig', workspace: 'docker-credentials' }
      ]
    }
  ]
}

// 获取基础镜像
const getBaseImageForLanguage = (languageName: string) => {
  const imageMap: Record<string, string> = {
    'Java': 'openjdk:11-jdk-slim',
    'Node.js': 'node:16-alpine',
    'Python': 'python:3.9-slim',
    'Go': 'golang:1.19-alpine',
    'PHP': 'php:8.1-cli-alpine'
  }
  return imageMap[languageName] || 'alpine:latest'
}

// 获取构建命令
const getBuildCommandForLanguage = (languageName: string) => {
  const commandMap: Record<string, string> = {
    'Java': 'mvn clean package -DskipTests',
    'Node.js': 'npm ci && npm run build',
    'Python': 'pip install -r requirements.txt',
    'Go': 'go build -o app .',
    'PHP': 'composer install --no-dev --optimize-autoloader'
  }
  return commandMap[languageName] || 'echo "No build command defined"'
}

// 参数管理
const addParameter = () => {
  pipelineConfig.value.parameters.push({
    name: '',
    type: 'string',
    default_value: '',
    description: ''
  })
}

const removeParameter = (index: number) => {
  pipelineConfig.value.parameters.splice(index, 1)
}

// 工作空间管理
const addWorkspace = () => {
  pipelineConfig.value.workspaces.push({
    name: '',
    description: '',
    optional: false
  })
}

// 自动创建默认工作空间
const createDefaultWorkspaces = () => {
  if (pipelineConfig.value.workspaces.length === 0) {
    pipelineConfig.value.workspaces.push(
      {
        name: 'shared-data',
        description: '共享数据存储，用于在任务间传递文件',
        optional: false
      },
      {
        name: 'docker-credentials',
        description: 'Docker镜像仓库凭证',
        optional: true
      }
    )
    ElMessage.success('已自动创建默认工作空间')
  }
}

const addTaskWorkspace = (index: number) => {
  const task = pipelineConfig.value.tasks[index]
  const defaultWorkspaceName = getDefaultWorkspaceNameForTask(task.type)
  const defaultWorkspace = pipelineConfig.value.workspaces.length > 0 
    ? pipelineConfig.value.workspaces[0].name 
    : 'shared-data'
  
  task.workspaces.push({
    name: defaultWorkspaceName,
    workspace: defaultWorkspace,
  }) 
}

// 获取任务类型的默认工作空间名称
const getDefaultWorkspaceNameForTask = (taskType: string) => {
  const workspaceMap: Record<string, string> = {
    'git-clone': 'output',
    'build-app': 'source',
    'build-image': 'source',
    'run-tests': 'source',
    'deploy-app': 'source',
    'custom-script': 'source'
  }
  return workspaceMap[taskType] || 'source'
}

const removeTaskWorkspace = (index: number, wsIndex: number) => {
  pipelineConfig.value.tasks[index].workspaces.splice(wsIndex, 1)
}

const removeWorkspace = (index: number) => {
  pipelineConfig.value.workspaces.splice(index, 1)
}

// 检查工作空间是否在当前任务中已被使用
const isWorkspaceUsedInTask = (task: PipelineTask, workspaceName: string, currentIndex: number) => {
  return task.workspaces.some((ws, index) => 
    index !== currentIndex && ws.workspace === workspaceName
  )
}

// 获取工作空间的使用统计信息
const getWorkspaceUsageInfo = (workspaceName: string) => {
  let totalUsage = 0
  const usedInTasks: string[] = []
  
  pipelineConfig.value.tasks.forEach(task => {
    const usageCount = task.workspaces.filter(ws => ws.workspace === workspaceName).length
    if (usageCount > 0) {
      totalUsage += usageCount
      usedInTasks.push(task.name || '未命名任务')
    }
  })
  
  return {
    totalUsage,
    usedInTasks,
    isUsed: totalUsage > 0
  }
}

// 获取工作空间选项的标签
const getWorkspaceOptionLabel = (workspace: PipelineWorkspace, task: PipelineTask, currentIndex: number) => {
  let label = workspace.name
  if (workspace.description) {
    label += ` (${workspace.description})`
  }
  
  // 检查是否在当前任务中已被使用
  const isUsedInCurrentTask = isWorkspaceUsedInTask(task, workspace.name, currentIndex)
  if (isUsedInCurrentTask) {
    label += ' - 已在此任务中使用'
  }
  
  return label
}

// 任务管理
const addTask = () => {
  pipelineConfig.value.tasks.push({
    name: '',
    type: 'custom-script',
    image: 'alpine:latest',
    script: '',
    runAfter: [],
    workspaces: []
  })
}

const removeTask = (index: number) => {
  pipelineConfig.value.tasks.splice(index, 1)
}

// 生成YAML
const generateYaml = () => {
  const config = pipelineConfig.value
  
  if (!config.name) {
    return '# 请先配置流水线基本信息'
  }
  
  const yamlParts: string[] = []
  
  // 基础元数据
  yamlParts.push(`apiVersion: tekton.dev/v1beta1
kind: Pipeline
metadata:
  name: ${config.name || 'unnamed-pipeline'}
  labels:
    app.kubernetes.io/name: ${config.name || 'unnamed-pipeline'}
    language: ${props.sourceData?.language_code || 'unknown'}
    type: ${config.pipeline_type || 'template'}
    source-type: ${props.sourceType || 'language'}`)

  if (config.description) {
    yamlParts.push(`  annotations:
    description: "${config.description}"`)
  }

  // Spec开始
  yamlParts.push(`spec:`)
  
  if (config.description) {
    yamlParts.push(`  description: "${config.description}"`)
  }

  // 参数配置
  if (config.parameters && config.parameters.length > 0) {
    yamlParts.push(`  params:`)
    config.parameters.forEach(param => {
      if (param.name) {
        yamlParts.push(`    - name: ${param.name}`)
        yamlParts.push(`      type: ${param.type || 'string'}`)
        if (param.description) {
          yamlParts.push(`      description: "${param.description}"`)
        }
        if (param.default_value) {
          yamlParts.push(`      default: "${param.default_value}"`)
        }
      }
    })
  }

  // 工作空间配置
  if (config.workspaces && config.workspaces.length > 0) {
    yamlParts.push(`  workspaces:`)
    config.workspaces.forEach(ws => {
      if (ws.name) {
        yamlParts.push(`    - name: ${ws.name}`)
        if (ws.description) {
          yamlParts.push(`      description: "${ws.description}"`)
        }
        if (ws.optional) {
          yamlParts.push(`      optional: true`)
        }
      }
    })
  }

  // 任务配置
  if (config.tasks && config.tasks.length > 0) {
    yamlParts.push(`  tasks:`)
    config.tasks.forEach(task => {
      if (task.name) {
        yamlParts.push(`    - name: ${task.name}`)
        
        // 依赖任务
        if (task.runAfter && task.runAfter.length > 0) {
          const validDependencies = task.runAfter.filter(dep => dep && dep.trim())
          if (validDependencies.length > 0) {
            yamlParts.push(`      runAfter:`)
            validDependencies.forEach(dep => {
              yamlParts.push(`        - ${dep}`)
            })
          }
        }
        
        // 根据任务类型生成不同的配置
        switch (task.type) {
          case 'git-clone':
            yamlParts.push(`      taskRef:
        name: git-clone
        kind: ClusterTask`)
            if (config.workspaces.some(ws => ws.name === 'shared-data')) {
              yamlParts.push(`      workspaces:
        - name: output
          workspace: shared-data`)
            }
            if (config.parameters.some(p => p.name === 'git-url') || config.parameters.some(p => p.name === 'git-revision')) {
              yamlParts.push(`      params:`)
              if (config.parameters.some(p => p.name === 'git-url')) {
                yamlParts.push(`        - name: url
          value: $(params.git-url)`)
              }
              if (config.parameters.some(p => p.name === 'git-revision')) {
                yamlParts.push(`        - name: revision
          value: $(params.git-revision)`)
              }
            }
            break
            
          case 'build-image':
            yamlParts.push(`      taskRef:
        name: buildah
        kind: ClusterTask`)
            yamlParts.push(`      workspaces:`)
            if (config.workspaces.some(ws => ws.name === 'shared-data')) {
              yamlParts.push(`        - name: source
          workspace: shared-data`)
            }
            if (config.workspaces.some(ws => ws.name === 'docker-credentials')) {
              yamlParts.push(`        - name: dockerconfig
          workspace: docker-credentials`)
            }
            if (config.parameters.some(p => p.name === 'image-name') || config.parameters.some(p => p.name === 'image-tag')) {
              yamlParts.push(`      params:`)
              if (config.parameters.some(p => p.name === 'image-name') && config.parameters.some(p => p.name === 'image-tag')) {
                yamlParts.push(`        - name: IMAGE
          value: $(params.image-name):$(params.image-tag)`)
              } else if (config.parameters.some(p => p.name === 'image-name')) {
                yamlParts.push(`        - name: IMAGE
          value: $(params.image-name):latest`)
              }
              yamlParts.push(`        - name: DOCKERFILE
          value: ./Dockerfile
        - name: CONTEXT
          value: .`)
            }
            break
            
                     case 'build-app':
           case 'run-tests':
           case 'deploy-app':
           case 'custom-script':
           default:
             yamlParts.push(`      taskSpec:`)
             
             // 工作空间
             if (config.workspaces.length > 0) {
               yamlParts.push(`        workspaces:`)
               yamlParts.push(`          - name: source`)
             }
             
             // 参数
             if (task.type === 'deploy-app' && config.parameters.some(p => p.name.includes('image'))) {
               yamlParts.push(`        params:`)
               config.parameters.filter(p => p.name.includes('image')).forEach(param => {
                 yamlParts.push(`          - name: ${param.name}
            type: ${param.type}`)
               })
             }
             
             // 步骤
             yamlParts.push(`        steps:`)
             yamlParts.push(`          - name: ${task.name || 'step'}`)
             
             // 使用用户配置的镜像，如果没有则使用默认镜像
             const taskImage = task.image && task.image.trim() 
               ? task.image.trim() 
               : getBaseImageForLanguage(props.sourceData?.name || 'unknown')
             yamlParts.push(`            image: ${taskImage}`)
             
             // 工作目录
             if (config.workspaces.some(ws => ws.name === 'shared-data')) {
               yamlParts.push(`            workingDir: $(workspaces.source.path)`)
             }
             
             // 脚本内容 - 优先使用用户配置的脚本
             if (task.script && task.script.trim()) {
               yamlParts.push(`            script: |`)
               task.script.split('\n').forEach(line => {
                 yamlParts.push(`              ${line}`)
               })
             } else {
               // 如果用户没有配置脚本，则根据任务类型提供默认脚本
               const defaultScript = getDefaultScriptForTaskType(task.type, props.sourceData?.name)
               yamlParts.push(`            script: |`)
               defaultScript.split('\n').forEach(line => {
                 yamlParts.push(`              ${line}`)
               })
             }
             
             // 环境变量（如果有的话）
             if (task.type === 'deploy-app') {
               yamlParts.push(`            env:`)
               yamlParts.push(`              - name: IMAGE_NAME`)
               if (config.parameters.some(p => p.name === 'image-name')) {
                 yamlParts.push(`                value: $(params.image-name)`)
               } else {
                 yamlParts.push(`                value: "app:latest"`)
               }
             }
             
             // 工作空间绑定
             if (task.workspaces && task.workspaces.length > 0) {
               yamlParts.push(`      workspaces:`)
               task.workspaces.forEach(ws => {
                 if (ws.name && ws.workspace) {
                   yamlParts.push(`        - name: ${ws.name}`)
                   yamlParts.push(`          workspace: ${ws.workspace}`)
                 }
               })
             } else if (config.workspaces.some(ws => ws.name === 'shared-data')) {
               yamlParts.push(`      workspaces:
        - name: source
          workspace: shared-data`)
             }
             break
        }
      }
    })
  }

  // 如果没有任务，添加注释提示
  if (!config.tasks || config.tasks.length === 0) {
    yamlParts.push(`  # 暂无任务配置，请在"任务配置"标签页中添加任务`)
  }

  return yamlParts.join('\n')
}

// 获取任务类型的默认脚本
const getDefaultScriptForTaskType = (taskType: string, languageName?: string) => {
  switch (taskType) {
    case 'build-app':
      return getBuildCommandForLanguage(languageName || 'unknown')
    case 'run-tests':
      return getTestCommandForLanguage(languageName || 'unknown')
    case 'deploy-app':
      return `#!/bin/sh
set -e
echo "开始部署应用..."
# 部署脚本将在这里执行
echo "部署完成"`
    default:
      return `#!/bin/sh
set -e
echo "执行自定义脚本..."
# 在这里添加您的脚本内容
echo "脚本执行完成"`
  }
}

// 获取测试命令
const getTestCommandForLanguage = (languageName: string) => {
  const commandMap: Record<string, string> = {
    'Java': `#!/bin/sh
set -e
echo "运行Java测试..."
mvn test`,
    'Node.js': `#!/bin/sh
set -e
echo "运行Node.js测试..."
npm test`,
    'Python': `#!/bin/sh
set -e
echo "运行Python测试..."
python -m pytest`,
    'Go': `#!/bin/sh
set -e
echo "运行Go测试..."
go test ./...`,
    'PHP': `#!/bin/sh
set -e
echo "运行PHP测试..."
./vendor/bin/phpunit`
  }
  return commandMap[languageName] || `#!/bin/sh
set -e
echo "运行测试..."
# 在这里添加测试命令
echo "测试完成"`
}

// 获取任务类型对应的颜色
const getTaskTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'git-clone': 'primary',
    'build-app': 'success',
    'build-image': 'warning',
    'run-tests': 'info',
    'deploy-app': 'danger',
    'custom-script': 'default'
  }
  return colorMap[type] || 'default'
}

// 刷新YAML内容
const refreshYaml = () => {
  yamlContent.value = generateYaml()
  ElMessage.success('YAML内容已刷新')
}

// YAML生成辅助
function generateYamlFromConfig(config) {
  if (!config || !config.name) return '# 请先配置流水线基本信息';
  const yamlParts: string[] = [];
  yamlParts.push(`apiVersion: tekton.dev/v1beta1\nkind: Pipeline\nmetadata:\n  name: ${config.name || 'unnamed-pipeline'}\n  labels:\n    app.kubernetes.io/name: ${config.name || 'unnamed-pipeline'}\n    language: ${config.language_code || 'unknown'}\n    type: ${config.type || 'template'}\n    pipeline-type: ${config.pipeline_type || 'microapp'}`);
  if (config.description) {
    yamlParts.push(`  annotations:\n    description: \"${config.description}\"`);
  }
  yamlParts.push(`spec:`);
  if (config.description) {
    yamlParts.push(`  description: \"${config.description}\"`);
  }
  if (config.parameters && config.parameters.length > 0) {
    yamlParts.push(`  params:`);
    config.parameters.forEach(param => {
      if (param.name) {
        yamlParts.push(`    - name: ${param.name}`);
        yamlParts.push(`      type: ${param.type || 'string'}`);
        if (param.description) {
          yamlParts.push(`      description: \"${param.description}\"`);
        }
        if (param.default_value) {
          yamlParts.push(`      default: \"${param.default_value}\"`);
        }
      }
    });
  }
  if (config.workspaces && config.workspaces.length > 0) {
    yamlParts.push(`  workspaces:`);
    config.workspaces.forEach(ws => {
      if (ws.name) {
        yamlParts.push(`    - name: ${ws.name}`);
        if (ws.description) {
          yamlParts.push(`      description: \"${ws.description}\"`);
        }
        if (ws.optional) {
          yamlParts.push(`      optional: true`);
        }
      }
    });
  }
  if (config.tasks && config.tasks.length > 0) {
    yamlParts.push(`  tasks:`);
    config.tasks.forEach(task => {
      if (task.name) {
        yamlParts.push(`    - name: ${task.name}`);
        yamlParts.push(`      taskRef:`);
        yamlParts.push(`        name: ${task.type}`);
        if (task.image) {
          yamlParts.push(`      image: ${task.image}`);
        }
        if (task.runAfter && task.runAfter.length > 0) {
          yamlParts.push(`      runAfter: [${task.runAfter.map(x => `'${x}'`).join(', ')}]`);
        }
        // 任务工作空间绑定
        if (task.workspaces && task.workspaces.length > 0) {
          yamlParts.push(`      workspaces:`);
          task.workspaces.forEach(ws => {
            if (ws.name && ws.workspace) {
              yamlParts.push(`        - name: ${ws.name}`);
              yamlParts.push(`          workspace: ${ws.workspace}`);
            }
          });
        }
      }
    });
  }
  return yamlParts.join('\n');
}

const copyYaml = (mode) => {
  let yaml = ''
  if (mode === 'inherit') {
    yaml = generateYamlFromConfig(inheritedPipelineConfig.value)
  } else {
    yaml = yamlContent.value
  }
  navigator.clipboard.writeText(yaml)
  ElMessage.success('已复制到剪贴板')
}

// 重置配置
const resetConfig = async () => {
  try {
    // 重置所有状态
    resetAllStates()
    
    // 重新初始化配置
    await nextTick()
    await initializeConfig()
    
    ElMessage.success('配置已重置')
  } catch (error) {
    console.error('重置配置失败:', error)
    ElMessage.error('重置配置失败')
  }
}

// 处理保存
const handleSave = async () => {
  if (props.sourceType === 'language') {
    // 语言模式：保存语言Pipeline模板
    try {
      saving.value = true
      
      if (currentPipelineId.value > 0) {
        // 更新现有Pipeline
        await pipelineApi.updatePipeline(currentPipelineId.value, {
          name: pipelineConfig.value.name,
          source_id: props.sourceData?.id,
          source_type: props.sourceType,
          pipeline_type: pipelineConfig.value.pipeline_type,
          pipeline_config: pipelineConfig.value,
          description: pipelineConfig.value.description
        })
      } else {
        // 创建新Pipeline
        const result = await pipelineApi.createPipeline({
          name: pipelineConfig.value.name,
          source_id: props.sourceData?.id,
          source_type: props.sourceType,
          pipeline_type: pipelineConfig.value.pipeline_type,
          pipeline_config: pipelineConfig.value,
          description: pipelineConfig.value.description,
          is_default: true,
        })
        
        const newPipeline = handleAPIResponse(result) as any
        
        // 更新语言的pipeline_id
        if (newPipeline.id) {
          // 这里需要调用语言更新API
          // await languageApi.updateLanguage(props.sourceData.id, { pipeline_id: newPipeline.id })
        }
        
        // 重置创建状态
        isCreatingTemplate.value = false
      }
      
      hasChanges.value = false
      ElMessage.success('语言流水线模板保存成功')
      emit('saved')
      visible.value = false
    } catch (error) {
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
    } finally {
      saving.value = false
    }
  } else if (props.sourceType === 'microapp' && pipelineMode.value === 'inherit') {
    // 继承模式：清除应用的自定义Pipeline
    try {
      saving.value = true
      
      if (currentPipelineId.value > 0) {
        // 删除现有的自定义Pipeline
        await pipelineApi.deletePipeline(currentPipelineId.value)
        
        // 更新应用的pipeline_id为0
        // 这里需要调用应用更新API
        // await microappApi.updateMicroapp(props.sourceData.id, { pipeline_id: 0 })
      }
      
      ElMessage.success('已切换到继承模式，将使用语言默认流水线')
      emit('saved')
      visible.value = false
    } catch (error) {
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
    } finally {
      saving.value = false
    }
  } else {
    // 自定义模式：保存Pipeline配置
    try {
      saving.value = true
      
      if (currentPipelineId.value > 0) {
        // 更新现有Pipeline
        await pipelineApi.updatePipeline(currentPipelineId.value, {
          name: pipelineConfig.value.name,
          source_id: props.sourceData?.id,
          pipeline_type: pipelineConfig.value.pipeline_type,
          pipeline_config: pipelineConfig.value,
          description: pipelineConfig.value.description
        })
      } else {
        // 创建新Pipeline
        const result = await pipelineApi.createPipeline({
          name: pipelineConfig.value.name,
          source_id: props.sourceData?.id,
          source_type: props.sourceType,
          pipeline_type: pipelineConfig.value.pipeline_type,
          pipeline_config: pipelineConfig.value,
          description: pipelineConfig.value.description,
          is_default: false,
        })
        
        const newPipeline = handleAPIResponse(result) as any
        
        // 更新应用的pipeline_id
        if (props.sourceType === 'microapp' && newPipeline.id) {
          // 这里需要调用应用更新API
          // await microappApi.updateMicroapp(props.sourceData.id, { pipeline_id: newPipeline.id })
        }
      }
      
      hasChanges.value = false
      ElMessage.success('流水线配置保存成功')
      emit('saved')
      visible.value = false
    } catch (error) {
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
    } finally {
      saving.value = false
    }
  }
}

// 处理关闭
const handleClose = () => {
  if (hasChanges.value) {
    ElMessageBox.confirm(
      '有未保存的更改，确定要关闭吗？',
      '确认关闭',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      // 用户确认关闭，重置状态并关闭
      resetAllStates()
      visible.value = false
    }).catch(() => {
      // 用户取消关闭
      ElMessage.info('已取消关闭')
    })
    return
  }
  
  // 没有变更，直接关闭
  visible.value = false
}

// 重置所有状态到初始值
const resetAllStates = () => {
  // 重置基本状态
  isCreatingTemplate.value = false
  hasChanges.value = false
  saving.value = false
  loading.value = false
  activeTab.value = 'basic'
  yamlContent.value = ''
  showPipelineDrawer.value = false
  yamlPreviewVisible.value = false
  hasInitialized.value = false
  
  // 重置继承相关状态
  pipelineMode.value = 'inherit'
  languagePipelineInfo.value = null
  inheritedPipelineConfig.value = null
  inheritedPipelineInfo.value = null
  
  // 重置流水线配置到默认值
  pipelineConfig.value = {
    name: '',
    pipeline_type: 'full',
    description: '',
    triggers: ['push'],
    parameters: [
      {
        name: 'git-url',
        type: 'string',
        default_value: '',
        description: 'Git repository URL'
      },
      {
        name: 'git-revision',
        type: 'string', 
        default_value: 'main',
        description: 'Git revision'
      },
      {
        name: 'image-name',
        type: 'string',
        default_value: '',
        description: 'Container image name'
      },
      {
        name: 'image-tag',
        type: 'string',
        default_value: 'latest',
        description: 'Container image tag'
      }
    ],
    workspaces: [
      {
        name: 'shared-data',
        description: 'Shared workspace for source code',
        optional: false
      },
      {
        name: 'docker-credentials',
        description: 'Docker registry credentials',
        optional: false
      }
    ],
    tasks: [
      {
        name: 'fetch-source',
        type: 'git-clone',
        image: '',
        script: '',
        runAfter: [],
        workspaces: []
      }
    ]
  }
}

// 监听visible变化，初始化配置
watch(() => props.visible, async (newVisible) => {
  if (newVisible && props.sourceData) {
    // 每次打开时完全重置状态
    resetAllStates()
    
    // 等待DOM更新完成再初始化配置
    await nextTick()
    await initializeConfig()
  } else if (!newVisible) {
    // 关闭时也重置状态，确保下次打开时是干净的状态
    resetAllStates()
  }
}, { immediate: true })

// 方法定义
const getAlertTitle = () => {
  if (props.sourceType === 'language') {
    return '流水线模板配置'
  } else if (props.sourceType === 'microapp') {
    return '应用流水线配置'
  }
  return '流水线配置管理'
}

const getTaskTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'git-clone': '代码克隆',
    'build-app': '构建应用',
    'build-image': '构建镜像', 
    'run-tests': '运行测试',
    'deploy-app': '部署应用',
    'custom-script': '自定义脚本'
  }
  return labelMap[type] || type
}

const handlePipelineModeChange = async (mode: string) => {
  if (mode === 'inherit') {
    await loadLanguagePipeline()
  } else if (mode === 'custom') {
    if (!currentPipelineId.value) {
      await createPipeline()
    } else {
      await initializeConfig()
    }
  }
}

const switchToCustomMode = async () => {
  pipelineMode.value = 'custom'
  if (!currentPipelineId.value) {
    await createPipeline()
  }
}

const switchToInheritMode = async () => {
  try {
    await ElMessageBox.confirm(
      '切换到继承模式将删除当前自定义流水线配置，确定要继续吗？',
      '确认切换',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    pipelineMode.value = 'inherit'
    await loadLanguagePipeline()
    ElMessage.success('已切换到继承模式')
  } catch {
    ElMessage.info('已取消切换')
  }
}

const loadLanguagePipeline = async () => {
  if (props.sourceType !== 'microapp') return
  
  try {
    loading.value = true
    
    // 重置继承相关状态
    languagePipelineInfo.value = null
    inheritedPipelineConfig.value = null
    inheritedPipelineInfo.value = null
    
    // 通过语言代码获取语言信息
    const languageCode = props.sourceData?.language_code
    if (!languageCode) {
      inheritedPipelineInfo.value = {
        source: '开发语言',
        description: '应用未配置开发语言'
      }
      return
    }
    
    // 获取所有语言列表，找到对应的语言
    const languagesResult = await languageApi.getLanguageByCode(languageCode)
    const targetLanguage = handleAPIResponse(languagesResult) as any
    console.log(targetLanguage, 'targetLanguage#####')
    
    if (targetLanguage && targetLanguage?.pipeline_id) {
      // 获取语言的Pipeline配置
      // const pipelineResponse = await pipelineApi.getInheritPipeline({
      //   source_type: 'language',
      //   source_id: targetLanguage.id
      // })
      const pipelineResponse = await pipelineApi.getPipeline(targetLanguage.pipeline_id)
      const pipelineData = handleAPIResponse(pipelineResponse) as any
      
      languagePipelineInfo.value = targetLanguage
      inheritedPipelineConfig.value = pipelineData.pipeline_config || pipelineData
      inheritedPipelineInfo.value = {
        source: `${targetLanguage.name} 语言`,
        description: `继承自 ${targetLanguage.name} 语言的默认流水线模板`
      }
    } else {
      inheritedPipelineInfo.value = {
        source: '开发语言',
        description: targetLanguage ? 
          `${targetLanguage.name} 语言暂未配置流水线模板` : 
          `未找到 ${languageCode} 语言配置`
      }
    }
  } catch (error) {
    console.error('加载语言Pipeline失败:', error)
    ElMessage.error('加载语言Pipeline配置失败')
    inheritedPipelineInfo.value = {
      source: '开发语言',
      description: '加载语言流水线配置失败'
    }
  } finally {
    loading.value = false
  }
}

const initFromLanguageTemplate = async () => {
  if (!inheritedPipelineConfig.value) {
    ElMessage.warning('没有可用的语言模板')
    return
  }
  
  try {
    // 基于语言模板初始化当前配置
    const templateConfig = JSON.parse(JSON.stringify(inheritedPipelineConfig.value))
    
    // 修改名称和描述以适应应用
    templateConfig.name = `${props.sourceData?.name.toLowerCase() || 'app'}-pipeline`
    templateConfig.description = `基于${languageName.value}语言模板创建的${props.sourceData?.name}应用流水线`
    
    pipelineConfig.value = templateConfig
    hasInitialized.value = true
    hasChanges.value = true
    
    // ElMessage.success('已从语言模板初始化配置')
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('从语言模板初始化失败')
  }
}

const resetToLanguageTemplate = async () => {
  try {
    await initFromLanguageTemplate()
    ElMessage.success('已重置为语言模板配置')
  } catch (error) {
    ElMessage.error('重置失败')
  }
}

// 组件名称定义
defineOptions({
  name: 'PipelineEditorEnhanced'
})
</script>

<script lang="ts">
export default {
  name: 'PipelineEditorEnhanced'
}
</script>

<style scoped>
.pipeline-drawer :deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 20px 20px 0;
}

.pipeline-drawer :deep(.el-drawer__body) {
  padding: 20px;
}

.pipeline-container {
  height: 100%;
}

.pipeline-header {
  margin-bottom: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.mt-4 {
  margin-top: 16px;
}

/* 继承配置样式 */
.inheritance-config .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.inheritance-config .radio-content {
  margin-left: 8px;
}

.inheritance-config .radio-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.inheritance-config .radio-description {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

/* 继承Pipeline显示样式 */
.inherited-pipeline .inherited-stats {
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 6px;
}

.inherited-pipeline .inherited-tasks h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

/* 任务流程样式 */
.task-flow {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.task-flow-item {
  flex: 0 0 auto;
  min-width: 200px;
}

.task-flow-item :deep(.el-card__body) {
  padding: 12px;
}

.task-flow-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.task-flow-header .task-name {
  font-weight: 500;
  font-size: 14px;
}

.task-detail,
.task-dependencies {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.task-detail:last-child,
.task-dependencies:last-child {
  margin-bottom: 0;
}

.task-detail .el-icon,
.task-dependencies .el-icon {
  font-size: 14px;
}

.task-arrow {
  color: var(--el-color-primary);
  font-size: 18px;
  margin: 0 8px;
}

/* 配置表单样式 */
.template-actions {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 6px;
}

.template-actions .el-button {
  margin-right: 8px;
}

/* 参数和工作空间配置 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 任务配置样式 */
.task-item {
  margin-bottom: 16px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.task-item :deep(.el-card__body) {
  padding: 16px;
}

/* YAML预览样式 */
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.preview-actions .el-button {
  margin-left: 8px;
}

.yaml-stats {
  margin-bottom: 20px;
}

.task-flow-preview {
  overflow-x: auto;
  padding: 8px 0;
}
.task-flow-scroll {
  display: flex;
  align-items: center;
}
.task-card {
  min-width: 180px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #f0f1f2;
  margin-right: 12px;
  padding: 10px 16px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.task-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}
.task-meta {
  font-size: 12px;
  color: #888;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.task-arrow {
  color: #409eff;
  font-size: 20px;
  margin-right: 12px;
}
.task-type-git-clone .el-tag { background: #e3f2fd; color: #1976d2; }
.task-type-build-app .el-tag { background: #e8f5e9; color: #388e3c; }
.task-type-build-image .el-tag { background: #fff3e0; color: #f57c00; }
.yaml-preview {
  background: #f7f7f7;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Fira Mono', 'Consolas', monospace;
  font-size: 13px;
  color: #333;
  overflow-x: auto;
  margin-top: 8px;
}

/* 工作空间配置样式 */
.task-workspaces {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  padding: 12px;
  background-color: var(--el-fill-color-blank);
}

.workspace-list {
  margin-bottom: 12px;
}

.workspace-item {
  margin-bottom: 8px;
  padding: 8px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.workspace-item:last-child {
  margin-bottom: 0;
}

.workspace-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-top: 8px;
  border-top: 1px dashed var(--el-border-color-light);
}

.workspace-actions .el-text {
  flex: 1;
}

/* 工作空间使用情况样式 */
.workspace-usage {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 底部操作区 */
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-flow {
    flex-direction: column;
  }
  
  .task-arrow {
    transform: rotate(90deg);
    margin: 8px 0;
  }
  
  .preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style> 