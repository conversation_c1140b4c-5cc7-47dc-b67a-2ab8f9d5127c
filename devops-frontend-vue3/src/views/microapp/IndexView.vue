<template>
  <div class="microapp-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h3>微应用管理</h3>
        <el-tag v-if="activeTab === 'favorite'" effect="dark" type="warning" class="ml-2">收藏</el-tag>
      </div>
      <div class="header-right">
        <el-button-group class="view-toggle">
          <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
            <el-icon>
              <Grid />
            </el-icon>
          </el-button>
          <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
            <el-icon>
              <List />
            </el-icon>
          </el-button>
        </el-button-group>
        <el-button type="primary" @click="handleCreate">
          <el-icon>
            <Plus />
          </el-icon>
          新建应用
        </el-button>
      </div>
    </div>

    <el-card shadow="never">

      <!-- 选项卡切换 -->
      <el-tabs v-model="activeTab" class="app-tabs" @tab-click="handleTabChange">
        <el-tab-pane label="全部应用" name="all"></el-tab-pane>
        <el-tab-pane label="收藏应用" name="favorite"></el-tab-pane>
      </el-tabs>

      <!-- 搜索筛选区 -->
      <el-card class="search-card" shadow="hover">
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="" style="margin-bottom: 5px;">
            <el-select v-model="searchForm.product_id" placeholder="请选择产品" clearable size="medium"
              style="min-width: 120px;width: 100%;">
              <el-option v-for="product in productOptions" :key="product.id" :label="product.name"
                :value="product.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="" style="margin-bottom: 5px;">
            <el-select v-model="searchForm.project_id" placeholder="请选择项目" clearable size="medium"
              style="min-width: 120px;width: 100%;">
              <el-option v-for="project in projectOptions" :key="project.id" :label="project.name"
                :value="project.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="" style="margin-bottom: 5px;">
            <el-select v-model="searchForm.category_name" placeholder="请选择类型" clearable size="medium"
              style="min-width: 120px;width: 100%;">
              <el-option label="前端应用" value="frontend" />
              <el-option label="后端服务" value="backend" />
              <el-option label="中间件" value="middleware" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="启用状态" style="margin-bottom: 0;">
          <el-switch v-model="searchForm.enabled" :active-value="1" :inactive-value="0" size="small" />
        </el-form-item> -->
          <el-form-item style="margin-bottom: 5px;">
            <el-input v-model="searchForm.search" placeholder="搜索…" clearable size="medium"
              style="min-width: 120px;width: 100%;" @keyup.enter="handleSearch" />
          </el-form-item>
          <el-form-item style="margin-bottom: 5px;">
            <el-button type="primary" size="small" @click="handleSearch" style="border-radius: 6px;">搜索</el-button>
            <el-button type="info" size="small" @click="handleReset"
              style="border-radius: 6px; margin-left: 8px;">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 卡片视图 -->
      <template v-if="viewMode === 'card'">
        <div v-loading="loading" class="app-cards">
          <el-row :gutter="20">
            <el-col v-for="app in displayApps" :key="app.id" :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-card class="app-card" shadow="hover">
                <div class="app-card-header">
                  <div class="app-name-wrapper">
                    <el-icon v-if="app.category_name === 'frontend'" class="app-type-icon">
                      <Monitor />
                    </el-icon>
                    <el-icon v-else-if="app.category_name === 'backend'" class="app-type-icon">
                      <Connection />
                    </el-icon>
                    <el-icon v-else class="app-type-icon">
                      <Box />
                    </el-icon>
                    <h4 class="app-name">{{ app.name }}</h4>
                  </div>
                  <div class="app-actions">
                    <el-icon :class="['favorite-icon', { 'is-favorite': app.is_favorite }]"
                      @click.stop="toggleFavorite(app, app.id)">
                      <Star />
                    </el-icon>
                  </div>
                </div>

                <div class="app-info">
                  <div class="app-id">应用编码: {{ app.app_code }}</div>
                  <div class="app-project">产品项目: {{ app.product?.name || '未分配' }} / {{ app.project?.name || '未分配' }}
                  </div>
                  <div class="app-version" v-if="app.description">描述: {{ app.description }}</div>
                </div>

                <div class="app-status-bar">
                  <el-tag :type="app.enabled ? 'success' : 'danger'" size="small">
                    {{ app.enabled ? '启用' : '禁用' }}
                  </el-tag>
                  <el-tag :type="getCategoryTagType(app.category_name || '')" size="small" class="ml-2">
                    {{ getCategoryLabel(app.category_name || '') }}
                  </el-tag>

                  <div class="health-indicator">
                    <el-tooltip :content="getHealthLabel('unknown')">
                      <div :class="['health-dot', 'health-unknown']"></div>
                    </el-tooltip>
                  </div>
                </div>

                <div class="app-tags" v-if="app.tag_list && app.tag_list.length">
                  <el-tag v-for="tag in app.tag_list" :key="tag" size="small" effect="plain" class="app-tag">
                    {{ tag }}
                  </el-tag>
                </div>

                <div class="app-footer">
                  <el-dropdown trigger="click" @command="handleCommand($event, app)">
                    <el-button type="primary" class="operations-btn" size="small">
                      操作
                      <el-icon class="el-icon--right">
                        <ArrowDown />
                      </el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="view">查看</el-dropdown-item>
                        <el-dropdown-item command="edit">编辑</el-dropdown-item>
                        <el-dropdown-item command="deploy">部署</el-dropdown-item>
                        <el-dropdown-item command="pipeline">
                          流水线
                        </el-dropdown-item>
                        <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>

                  <el-button type="success" size="small" @click="handleQuickDeploy(app)">快速部署</el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </template>

      <!-- 表格视图 -->
      <template v-else>
        <el-card class="table-card" shadow="never">
          <el-table :data="displayApps" v-loading="loading" border stripe>
            <el-table-column width="60">
              <template #default="{ row }">
                <el-icon :class="['favorite-icon', { 'is-favorite': row.is_favorite }]"
                  @click.stop="toggleFavorite(row, row.id)">
                  <Star />
                </el-icon>
              </template>
            </el-table-column>
            <el-table-column prop="app_code" label="应用编码" width="120" />
            <el-table-column label="应用名称" min-width="180">
              <template #default="{ row }">
                <div class="app-name-cell">
                  <el-icon v-if="row.category === 'frontend'" class="app-type-icon">
                    <Monitor />
                  </el-icon>
                  <el-icon v-else-if="row.category === 'backend'" class="app-type-icon">
                    <Connection />
                  </el-icon>
                  <el-icon v-else class="app-type-icon">
                    <Box />
                  </el-icon>
                  <span>{{ row.name }}</span>
                  <div v-if="row.health_status" :class="['health-dot-small', `health-${row.health_status}`]"></div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="project.name" label="所属项目" width="150">
              <template #default="{ row }">
                {{ row.project?.name }} / {{ row.product?.name }}
              </template>
            </el-table-column>
            <el-table-column prop="category" label="应用类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getCategoryTagType(row.category)">
                  {{ getCategoryLabel(row.category) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="enabled" label="启用状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.enabled ? 'success' : 'danger'">
                  {{ row.enabled ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <!-- <el-table-column label="标签" width="200">
            <template #default="{ row }">
              <el-tag 
                v-for="tag in row.app_tags || []" 
                :key="tag" 
                size="small" 
                effect="plain"
                class="app-tag"
              >
                {{ tag }}
              </el-tag>
            </template>
          </el-table-column> -->
            <!-- <el-table-column prop="version" label="版本" width="120" /> -->
            <el-table-column prop="created_time" label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="350" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="handleView(row)">
                  查看
                </el-button>
                <el-button size="small" type="primary" @click="handleEdit(row)">
                  编辑
                </el-button>
                <el-button size="small" type="success" @click="handleQuickDeploy(row)">
                  部署
                </el-button>
                <el-button size="small" type="info" @click="handlePipeline(row)">
                  <el-icon>
                    <Connection />
                  </el-icon>流水线
                </el-button>
                <el-popconfirm title="确定删除此应用吗？" @confirm="handleDelete(row)">
                  <template #reference>
                    <el-button size="small" type="danger">
                      删除
                    </el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </template>

      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination :current-page="pagination.page" :page-size="pagination.pageSize" :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>

    <!-- 应用详情抽屉 -->
    <el-drawer v-model="detailDrawer.visible" :title="detailDrawer.title" size="calc(100% - 240px)" direction="rtl">
      <div v-loading="detailDrawer.loading" class="app-detail-container">
        <template v-if="detailDrawer.app">
          <div class="detail-header">
            <div class="detail-title">
              <el-tag :type="getCategoryTagType(detailDrawer.app.category_name || '')" class="mr-2">
                {{ getCategoryLabel(detailDrawer.app.category_name || '') }}
              </el-tag>
              <h2>{{ detailDrawer.app.name }}</h2>
            </div>
            <div class="detail-actions">
              <el-button type="primary" @click="handleEdit(detailDrawer.app)">编辑</el-button>
              <el-button type="success" @click="handleDeploy(detailDrawer.app)">部署</el-button>
              <el-button type="warning" @click="handleRollback(detailDrawer.app)">回滚</el-button>
              <el-button type="info" @click="handleDockerfile(detailDrawer.app)">Dockerfile</el-button>
              <el-button type="info" @click="handlePipeline(detailDrawer.app)">
                流水线
              </el-button>
            </div>
          </div>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="应用编码">{{ detailDrawer.app.app_code }}</el-descriptions-item>
            <el-descriptions-item label="应用名称">{{ detailDrawer.app.name || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="所属产品">{{ detailDrawer.app.product?.name || '未分配' }}</el-descriptions-item>
            <el-descriptions-item label="所属项目">{{ detailDrawer.app.project?.name || '未分配' }}</el-descriptions-item>
            <el-descriptions-item label="启用状态">
              <el-tag v-if="detailDrawer.app.enabled" type="success">
                启用
              </el-tag>
              <el-tag v-else type="danger">
                禁用
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="仓库地址">
              <el-link type="primary" :href="detailDrawer.app.repo_settings?.http_url_to_repo" target="_blank">
                {{ detailDrawer.app.repo_settings?.http_url_to_repo || '未设置' }}
              </el-link>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDateTime(detailDrawer.app.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ formatDateTime(detailDrawer.app.updated_at) }}</el-descriptions-item>

            <el-descriptions-item label="应用描述" :span="2">
              {{ detailDrawer.app.description || '暂无描述' }}
            </el-descriptions-item>
          </el-descriptions>

        </template>
      </div>
    </el-drawer>

    <!-- 新建应用对话框 -->
    <MicroAppForm v-model:visible="createDialog.visible" :form-data="createDialog.formData"
      :is-edit="createDialog.isEdit" @success="handleCreateSuccess" />

    <!-- 部署对话框 -->
    <el-dialog v-model="deployDialog.visible" title="部署应用" width="600px">
      <div v-if="deployDialog.app">
        <p>应用名称：{{ deployDialog.app.name }}</p>
        <p>部署功能开发中...</p>
      </div>
      <template #footer>
        <el-button @click="deployDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="deployDialog.visible = false">确定</el-button>
      </template>
    </el-dialog>

    <!-- 编辑抽屉 -->
    <MicroAppForm v-model:visible="editDrawer.visible" :form-data="editDrawer.app" :is-edit="true"
      @success="handleEditSuccess" />

    <!-- Pipeline抽屉 -->
    <PipelineEditorEnhanced v-model:visible="pipelineDrawerVisible" :source-data="currentAppLanguage"
      source-type="microapp" @saved="handlePipelineSaved" />

    <!-- 添加模块级别流水线抽屉 -->
    <PipelineEditorEnhanced v-model:visible="appInfoPipelineDrawerVisible" :source-data="currentAppInfoLanguage"
      source-type="appinfo" @saved="handleAppInfoPipelineSaved" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Grid, List, Plus, Monitor, Connection, Box, Star, ArrowDown, Search } from '@element-plus/icons-vue'
import microappApi from '../../api/modules/microapp'
import type { MicroApp as APIMicroApp, MicroAppCreateRequest, AppInfo, MicroApp } from '../../api/modules/microapp'
import { useMicroappStore } from '../../stores/modules/microapp'
import { formatDateTime } from '../../utils/date'
import MicroAppForm from './components/MicroAppForm.vue'
import { handleAPIResponse } from '../../utils/response'
import { environmentApi, productApi, projectApi } from '../../api/modules/cmdb'
import PipelineEditorEnhanced from '../cmdb/language/components/PipelineEditorEnhanced.vue'

const microappStore = useMicroappStore()

// 视图模式
const viewMode = ref<'card' | 'table'>('card')

// 活动选项卡
const activeTab = ref('all')

// 搜索表单
const searchForm = reactive({
  search: '',
  product_id: undefined,
  project_id: undefined,
  category_name: undefined,
  enabled: undefined,
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 项目选项
const projectOptions = ref<{ id: number, name: string }[]>([])

const productOptions = ref<{ id: number, name: string }[]>([])

// 加载产品和项目
const loadProductAndProject = async () => {
  const [products, projects] = await Promise.all([
    productApi.getProducts(),
    projectApi.getProjects()
  ])
  console.log(products, "products")
  console.log(projects, "projects")
  const productData = handleAPIResponse(products) as any
  console.log(productData, "productData")
  productOptions.value = productData.items.map((item: any) => ({
    id: item.id,
    name: item.name
  }))
  const projectData = handleAPIResponse(projects) as any
  projectOptions.value = projectData.items.map((item: any) => ({
    id: item.id,
    name: item.name
  }))
}

// 详情抽屉
const detailDrawer = reactive({
  visible: false,
  title: '应用详情',
  uniq_tag: '',
  pipeline_id: 0,
  app: null as MicroApp | null,
  loading: false
})

// 部署历史数据
const deploymentHistory = ref<any[]>([])

// 资源使用情况
const resourceUsage = ref<any>({
  cpu: { current: 0, limit: 0 },
  memory: { current: 0, limit: 0 },
  storage: { current: 0, limit: 0 }
})

// 使用store中的数据
const loading = computed(() => microappStore.loading)

// 标签列表
const availableTags = ref<string[]>([])

// 新建应用对话框
const createDialog = reactive({
  visible: false,
  formData: null as MicroApp | null,
  isEdit: false
})

// 部署对话框
const deployDialog = reactive({
  visible: false,
  app: null as MicroApp | null
})

// 添加编辑抽屉响应式状态
const editDrawer = reactive({
  visible: false,
  title: '编辑应用',
  app: null as MicroApp | null,
  loading: false,
  repoLoading: false,
  branchesLoading: false,
  form: {
    app_code: '',
    name: '',
    product_id: undefined as number | undefined,
    project_id: undefined as number | undefined,
    category_name: '',
    description: '',
    repo_settings: {
      url: '',
      default_branch: '',
      owner: '',
      name: '',
      id: null as number | null,
      http_url_to_repo: '',
      path_with_namespace: '',
      description: ''
    },
    team_members: {} as { id: number, name: string }[],
    deployment_type: '',
    scan_branch: '',
    build_command: '',
    language_code: '',
    docker_settings: {
      mode: 'default',
      dockerfile: '',
      jar_path: ''
    },
    target_settings: {
      mode: 'default',
      path: ''
    },
    start_command: '',
  },
  rules: {
    name: [{ required: true, message: '应用名称不能为空', trigger: 'blur' }],
    category: [{ required: true, message: '请选择应用类型', trigger: 'change' }],
    'repo.url': [{ required: true, message: '仓库地址不能为空', trigger: 'blur' }]
  },
  branchGroups: [] as Array<{
    label: string,
    options: Array<{
      name: string,
      id: string,
      author_name: string,
      message: string,
      committed_date: string,
      short_id: string,
      protected: boolean
    }>
  }>,
  repoOptions: [] as Array<{
    id: number,
    name: string,
    description: string,
    http_url_to_repo: string,
    path_with_namespace: string
  }>,
  branchCommitInfo: null as {
    id: string;
    short_id: string;
    message: string;
    author_name: string;
    committed_date: string;
  } | null,
})

// 添加表单引用
const editFormRef = ref();

// 添加pipeline抽屉状态
const pipelineDrawerVisible = ref(false)
const currentApp = ref<MicroApp | null>(null)

// 计算当前应用对应的数据（适配PipelineEditorEnhanced）
const currentAppLanguage = computed(() => {
  if (!currentApp.value) return null

  // 将MicroApp转换为PipelineEditorEnhanced期望的格式
  return {
    id: currentApp.value.id,
    name: currentApp.value.name,
    language_code: currentApp.value.language_code,
    pipeline_id: currentApp.value.pipeline_id || 0,
    description: currentApp.value.description,
    created_at: currentApp.value.created_at,
    updated_at: currentApp.value.updated_at
  }
})
// 初始化编辑表单
// const initEditForm = (app: MicroApp) => {
//   editDrawer.form.name = app.name;
//   editDrawer.form.app_code = app.app_code || '';
//   editDrawer.form.project_id = app.project?.id;
//   editDrawer.form.category_name = app.category_name || 'backend';
//   editDrawer.form.description = app.description || '';
//   editDrawer.form.deployment_type = app.deployment_type || 'k8s';
//   editDrawer.form.scan_branch = app.scan_branch || '';
//   editDrawer.form.build_command = app.build_command || '';
//   editDrawer.form.language_code = app.language_code || '';

//   // 从repo中提取Product
//   const pathParts = app.repo?.path_with_namespace?.split('/') || [];
//   if (pathParts.length > 0) {
//     editDrawer.form.product_id = pathParts[0] || '';
//   }

//   // 处理Dockerfile类型
//   if (app.docker_settings && typeof app.docker_settings === 'object') {
//     if (app.docker_settings.mode) {
//       editDrawer.form.docker_settings.mode = app.docker_settings.mode;
//     }
//   } else {
//     editDrawer.form.docker_settings.mode = 'default';
//   }

//   // 处理Jar Target
//   editDrawer.form.target_settings.mode = app.target_settings.mode || 'default';

//   // 处理启动命令和备注
//   if (app.modules && typeof app.modules === 'object') {
//     editDrawer.form.start_command = app.modules.startup_command || '';
//   } else {
//     editDrawer.form.start_command = '';
//   }

//   editDrawer.form.description = app.description || '';

//   // 处理复杂对象
//   if (app.repo && typeof app.repo === 'object') {
//     // 确保repo对象有所有需要的字段
//     editDrawer.form.repo_settings = {
//       url: app.repo.http_url_to_repo || app.repo.url || '',
//       default_branch: app.repo.default_branch || '',
//       owner: app.repo.owner || '',
//       name: app.repo.name || '',
//       id: app.repo.id || null,
//       http_url_to_repo: app.repo.http_url_to_repo || '',
//       path_with_namespace: app.repo.path_with_namespace || '',
//       description: app.repo.description || ''
//     };

//   } else {
//     editDrawer.form.repo_settings = {
//       url: '',
//       default_branch: '',
//       owner: '',
//       name: '',
//       id: null,
//       http_url_to_repo: '',
//       path_with_namespace: '',
//       description: ''
//     };
//   }

//   if (app.team_members && Array.isArray(app.team_members)) {
//     editDrawer.form.team_members = [...app.team_members];
//   } else {
//     editDrawer.form.team_members = [];
//   }
// };

// 获取微应用列表
const getMicroappList = async () => {
  try {
    // 构建查询参数，使用any类型避免TypeScript错误
    const params: any = {
      page: pagination.page,
      page_size: pagination.pageSize
    };

    // 添加收藏过滤参数
    if (activeTab.value === 'favorite') {
      params.is_favorite = true;
    }

    // 添加搜索参数
    if (searchForm.search) params.search = searchForm.search;
    if (searchForm.project_id) params.project_id = searchForm.project_id;
    if (searchForm.product_id) params.product_id = searchForm.product_id;
    if (searchForm.category_name) params.category_name = searchForm.category_name;
    if (searchForm.enabled !== undefined) params.enabled = searchForm.enabled;

    await microappStore.getMicroapps(params);
    pagination.total = microappStore.total;
  } catch (error) {
    console.error('获取微应用列表失败:', error);
    ElMessage.error('获取微应用列表失败');
  }
}

// 根据当前筛选和选项卡计算显示应用列表
const displayApps = computed(() => {
  // 现在所有应用列表（包括收藏应用）都通过后端API获取和分页
  // 直接返回store中的数据
  return microappStore.microapps;
})

// 获取应用类型标签样式
const getCategoryTagType = (category: string) => {
  const typeMap: Record<string, string> = {
    'frontend': 'success',
    'backend': 'primary',
    'middleware': 'warning'
  }
  return typeMap[category] || 'info'
}

// 获取应用类型标签文字
const getCategoryLabel = (category: string) => {
  const labelMap: Record<string, string> = {
    'frontend': '前端应用',
    'backend': '后端服务',
    'middleware': '中间件'
  }
  return labelMap[category] || category
}

// 获取状态标签样式
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'active': 'success',
    'inactive': 'info',
    'pending': 'warning',
    'error': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取健康状态标签
const getHealthLabel = (status?: string) => {
  const labelMap: Record<string, string> = {
    'healthy': '健康',
    'warning': '警告',
    'error': '异常',
    'unknown': '未知'
  }
  return status ? labelMap[status] || '未知' : '未知'
}

// 获取环境标签类型
const getEnvTagType = (env: string) => {
  const typeMap: Record<string, string> = {
    'prod': 'danger',
    'pre': 'warning',
    'test': 'success',
    'dev': 'info'
  }
  return typeMap[env] || 'info'
}

// 获取部署状态类型
const getDeployStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'success': 'success',
    'failed': 'danger',
    'running': 'primary',
    'pending': 'info'
  }
  return typeMap[status] || 'info'
}

// 搜索处理函数
const handleSearch = () => {
  pagination.page = 1; // 重置到第一页
  getMicroappList();
}

// 重置搜索
const handleReset = () => {
  searchForm.search = ''
  searchForm.project_id = undefined
  searchForm.category_name = undefined
  searchForm.enabled = undefined
  pagination.page = 1
  getMicroappList()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  getMicroappList()
}

// 分页页码变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
}

// 处理选项卡切换
const handleTabChange = () => {
  pagination.page = 1; // 重置到第一页
  // 等待activeTab.value改变
  nextTick(() => {
    getMicroappList();
  })
}

// 切换收藏状态
const toggleFavorite = async (app: MicroApp, id: number) => {
  try {
    const isFavorite = await microappStore.toggleFavorite(id)
    app.is_favorite = isFavorite
    ElMessage.success(isFavorite ? '收藏成功' : '取消收藏成功')
    // 如果当前在收藏应用视图，刷新列表
    if (activeTab.value === 'favorite') {
      getMicroappList()
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    ElMessage.error('操作失败')
  }
}

// 下拉菜单命令处理
const handleCommand = (command: string, app: MicroApp) => {
  switch (command) {
    case 'view':
      handleView(app)
      break
    case 'edit':
      handleEdit(app)
      break
    case 'deploy':
      handleDeploy(app)
      break
    case 'pipeline':
      handlePipeline(app)
      break
    case 'delete':
      ElMessageBox.confirm(`确定删除应用 ${app.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        handleDelete(app)
      }).catch(() => { })
      break
  }
}

// 快速部署
const handleQuickDeploy = (app: MicroApp) => {
  ElMessageBox.prompt('请输入要部署的分支名', '快速部署', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPlaceholder: '例如: master, main, develop',
    inputPattern: /^[a-zA-Z0-9.\-_\/]+$/,
    inputErrorMessage: '分支名格式不正确'
  }).then(({ value }) => {
    deployApp(app.id, 'dev', value)
  }).catch(() => { })
}

// 部署应用
const deployApp = async (id: number, environment: string, branch: string) => {
  try {
    const response = await microappStore.deployMicroapp(id, {
      environment,
      branch
    })
    ElMessage.success(`已触发应用部署，任务ID: ${(response as any).task_id || '未知'}`)
  } catch (error) {
    console.error('部署失败:', error)
    ElMessage.error('部署失败')
  }
}

// 获取部署历史
const getDeployHistory = async (id: number) => {
  try {
    const res = await microappStore.getDeployHistory(id, {
      page: 1,
      page_size: 5
    })
    const result = handleAPIResponse(res) as { items: [], total: number }
    deploymentHistory.value = result.items || []
  } catch (error) {
    console.error('获取部署历史失败:', error)
    deploymentHistory.value = []
  }
}

// 获取资源使用情况
const getResourceUsage = async (id: number) => {
  try {
    const res = await microappStore.getResourcesUsage(id)
    resourceUsage.value = res || {
      cpu: { current: 0, limit: 0 },
      memory: { current: 0, limit: 0 },
      storage: { current: 0, limit: 0 }
    }
  } catch (error) {
    console.error('获取资源使用情况失败:', error)
  }
}

// 新建应用
const handleCreate = () => {
  createDialog.formData = null
  createDialog.isEdit = false
  createDialog.visible = true
}

// 处理新建应用成功
const handleCreateSuccess = () => {
  createDialog.visible = false
  // ElMessage.success('应用创建成功')
  getMicroappList() // 刷新列表
}

// 处理编辑应用成功
const handleEditSuccess = () => {
  editDrawer.visible = false
  // ElMessage.success('应用编辑成功')
  getMicroappList() // 刷新列表
}

// 查看详情
const handleView = async (app: MicroApp) => {
  detailDrawer.visible = true
  detailDrawer.title = `应用详情 - ${app.name}`
  detailDrawer.loading = true

  try {
    // 获取详细信息
    const detail = await microappStore.getMicroappDetail(app.id) as any
    detailDrawer.app = detail

    // 获取部署历史
    // getDeployHistory(app.id)

    // 获取资源使用情况
    // getResourceUsage(app.id)
    // 加载AppInfo
    appId.value = app.id
    // await loadAppInfoList()
  } catch (error) {
    console.error('获取应用详情失败:', error)
    ElMessage.error('获取应用详情失败')
  } finally {
    detailDrawer.loading = false
  }
}

// 编辑应用
const handleEdit = (app: MicroApp) => {
  editDrawer.app = app;
  editDrawer.title = `编辑应用 - ${app.name}`;
  // initEditForm(app);
  editDrawer.visible = true;
}

// 部署应用
const handleDeploy = (app: MicroApp) => {
  deployDialog.app = app
  deployDialog.visible = true
}

// 删除应用
const handleDelete = async (app: MicroApp) => {
  try {
    await microappStore.deleteMicroapp(app.id)
    ElMessage.success('删除成功')
    getMicroappList() // 刷新列表
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 监听分页变化
watch(() => pagination.page, () => {
  // 更新显示的应用列表
  getMicroappList()
})

// 处理Tekton Pipeline
const handlePipeline = async (app: MicroApp) => {
  if (!app) return;

  console.log('处理Pipeline配置，应用信息:', app);
  console.log('应用Pipeline ID:', app.pipeline_id);
  console.log('应用语言代码:', app.language_code);

  currentApp.value = app;
  pipelineDrawerVisible.value = true;
};

// 处理Pipeline保存成功
const handlePipelineSaved = () => {
  // ElMessage.success('Pipeline配置保存成功');
  pipelineDrawerVisible.value = false;
  // 刷新应用列表以获取最新的pipeline_id
  getMicroappList();
};

// 处理回滚
const handleRollback = async (app: MicroApp) => {
  if (!app) return;
  // 获取部署历史
  await getDeployHistory(app.id);
  // 只允许回滚到成功的历史版本
  const successHistory = deploymentHistory.value.filter((item: any) => item.status === 'success');
  if (successHistory.length === 0) {
    ElMessage.warning('没有可回滚的历史版本');
    return;
  }
  // 弹窗选择回滚目标
  ElMessageBox.prompt(
    successHistory.map((item: any, idx: number) => `【${idx + 1}】版本: ${item.version}，环境: ${item.environment}，时间: ${item.created_at}`).join('\n'),
    '选择要回滚的历史版本（输入序号）',
    {
      confirmButtonText: '回滚',
      cancelButtonText: '取消',
      inputPattern: /^\d+$/,
      inputErrorMessage: '请输入有效的序号',
      inputPlaceholder: '如: 1',
      closeOnClickModal: false,
      closeOnPressEscape: false
    }
  ).then(async ({ value }) => {
    const idx = parseInt(value, 10) - 1;
    const target = successHistory[idx];
    if (!target) {
      ElMessage.error('选择的历史版本不存在');
      return;
    }
    try {
      // 回滚API（假设cicd-service暴露了createRollback）
      // 兼容TypeScript类型检查
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const cicdApi = require('@/api/modules/cicd').default;
      await cicdApi.createRollback({
        app_id: app.id,
        environment: target.environment,
        version: target.version
      });
      ElMessage.success('回滚操作已触发');
      getDeployHistory(app.id);
    } catch (error) {
      console.error('回滚失败:', error);
      ElMessage.error('回滚失败');
    }
  }).catch(() => { });
};

// 处理Dockerfile
const handleDockerfile = async (app: MicroApp) => {
  if (!app) return;
  let dockerfileContent = '';
  try {
    const res = await microappStore.getDockerfile(app.id);
    const result = handleAPIResponse(res) as any
    dockerfileContent = result.config_value || '';
  } catch (error) {
    ElMessage.error('获取Dockerfile失败');
    return;
  }
  let inputValue = dockerfileContent;
  // 使用Element Plus自定义弹窗
  ElMessageBox({
    title: `编辑Dockerfile - ${app.name}`,
    message: '<textarea id="dockerfile-edit-area" style="width:100%;min-height:300px;resize:vertical;">' +
      (inputValue ? inputValue.replace(/</g, '&lt;').replace(/>/g, '&gt;') : '') + '</textarea>',
    dangerouslyUseHTMLString: true,
    showCancelButton: true,
    confirmButtonText: '保存',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        // 获取textarea内容
        const textarea = document.getElementById('dockerfile-edit-area') as HTMLTextAreaElement;
        if (!textarea) {
          ElMessage.error('未找到输入区域');
          return;
        }
        const newContent = textarea.value;
        try {
          await microappStore.updateDockerfile(app.id, { content: newContent });
          ElMessage.success('Dockerfile已保存');
          done();
        } catch (error) {
          ElMessage.error('保存失败');
        }
      } else {
        done();
      }
    }
  });
};

onMounted(() => {
  getMicroappList()
  loadProductAndProject()
})

// AppInfo管理区相关
const appInfoEnvs = ref<{ name: string; alias?: string }[]>([])

// 获取环境列表
const loadAppInfoEnvs = async () => {
  const res = await environmentApi.getEnvironments()
  const result = handleAPIResponse(res) as { items: [] }
  appInfoEnvs.value = (result.items || []).map((env: any) => ({ name: env.name, alias: env.alias }))
  if (appInfoEnvs.value.length > 0 && !appInfoTab.value) {
    appInfoTab.value = appInfoEnvs.value[0].name
  }
}

// 判断当前Tab环境是否有appinfo
const hasAppInfo = computed(() => {
  return !!appInfoList.value.find(i => i.environment?.name?.toLowerCase() === appInfoTab.value.toLowerCase())
})

// Tab切换时
const onAppInfoTabChange = (tab: any) => {
  appInfoTab.value = tab.paneName || tab.name || tab
}

// 打开编辑/添加弹窗
const openEditAppInfo = async () => {
  if (hasAppInfo.value) {
    // 编辑，填充表单
    const info = appInfoList.value.find(i => i.environment?.name?.toLowerCase() === appInfoTab.value.toLowerCase())
    Object.assign(editAppInfoForm, info, {
      app_id: appId.value // 确保设置 app_id
    })
  } else {
    // 添加，清空表单
    Object.assign(editAppInfoForm, {
      id: 0,
      app_id: appId.value, // 确保设置 app_id
      allow_ci_branch: [],
      allow_cd_branch: [],
      branch: '',
      build_command: '',
      ports: [],
      kubernetes_ids: [],
      kubernetes_info: {},
      desc: '',
      enable: true
    })
  }
  editAppInfoDialogVisible.value = true

  // 加载仓库分支信息
  // await loadBranchesForAppInfo()

  // 加载Kubernetes列表
  // await loadKubernetesList()
}


// 页面初始化
onMounted(() => {
  loadAppInfoEnvs()
})

const appId = ref<number | null>(null) // 当前微应用ID，需根据实际页面传递
const appInfoTab = ref('')
const appInfoList = ref<AppInfo[]>([])
const currentAppInfo = computed(() => appInfoList.value.find(i => i.environment?.name?.toLowerCase() === appInfoTab.value.toLowerCase()) || {})
const editAppInfoDialogVisible = ref(false)
const branchesLoading = ref(false) // 分支加载状态
const editAppInfoForm = reactive({
  id: 0,
  allow_ci_branch: [] as string[],
  allow_cd_branch: [] as string[],
  branch: '',
  build_command: '',
  ports: [] as Array<{ port: number; node_port: number; protocol: string }>,
  kubernetes_ids: [] as number[],
  kubernetes_info: {} as Object,
  desc: '',
  enable: true
})

// Ports操作
const addPort = () => {
  editAppInfoForm.ports.push({ port: 80, node_port: 30000, protocol: 'TCP' })
}
const removePort = (idx: number) => {
  editAppInfoForm.ports.splice(idx, 1)
}

const userGrantDialogVisible = ref(false)
const userGrantForm = reactive({ user: '' })
const userGrantUser = ref('')
const userGrantUserList = ref([
  { id: '1', name: 'Alice' },
  { id: '2', name: 'Bob' }
])
const onUserGrantSave = () => {
  userGrantDialogVisible.value = false
  // 授权逻辑，实际应调用API
  ElMessage.success('授权成功')
}

// branchesLoading状态已在上方定义

// 添加Kubernetes列表和加载状态
const kubernetesOptions = ref<{ id: number; name: string; alias?: string }[]>([])
const kubernetesLoading = ref(false)


// 添加模块流水线相关变量
const appInfoPipelineDrawerVisible = ref(false)
const currentAppInfoForPipeline = ref<AppInfo | null>(null)

// 计算当前AppInfo对应的语言对象（适配PipelineEditorEnhanced）
const currentAppInfoLanguage = computed(() => {
  if (!currentAppInfoForPipeline.value || !detailDrawer.app) return null

  // 将AppInfo转换为PipelineEditorEnhanced期望的language格式
  return {
    id: currentAppInfoForPipeline.value.id || 0,
    name: currentAppInfoForPipeline.value.uniq_tag || 'AppInfo',
    language_code: detailDrawer.app.language_code,
    description: currentAppInfoForPipeline.value.description || '',
    sort_order: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
})

// 处理应用模块Pipeline
const handleAppInfoPipeline = (appInfo: AppInfo) => {
  if (!appInfo || !appInfo.id) {
    ElMessage.warning('请先选择有效的应用模块')
    return
  }

  currentAppInfoForPipeline.value = appInfo
  appInfoPipelineDrawerVisible.value = true
}

// 处理AppInfo Pipeline保存成功
const handleAppInfoPipelineSaved = () => {
  ElMessage.success('模块Pipeline配置保存成功')
  appInfoPipelineDrawerVisible.value = false
}
</script>
<style lang="scss" scoped>
.microapp-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
      }
    }

    .header-right {
      display: flex;
      align-items: center;

      .view-toggle {
        margin-right: 12px;
      }
    }
  }

  .app-tabs {
    margin-bottom: 20px;
  }

  .search-card {
    background: #f8fafd;
    border-radius: 16px;
    margin-bottom: 20px;

    .search-form {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .el-form-item {
        margin-right: 12px;
        margin-bottom: 0;
        display: flex;
        align-items: center;
      }

      .el-input,
      .el-select,
      .el-switch,
      .el-button {
        height: 32px;
        line-height: 32px;
      }

      .el-button {
        min-width: 64px;
        border-radius: 6px;
        font-weight: 500;
        padding: 0 16px;
      }
    }
  }

  .app-cards {
    .app-card {
      border-radius: 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
      // padding: 18px 18px 14px 18px;
      // min-height: 220px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      transition: box-shadow 0.2s, transform 0.2s;

      &:hover {
        box-shadow: 0 8px 32px rgba(64, 158, 255, 0.18);
        transform: translateY(-4px) scale(1.02);
      }

      .app-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;

        .app-name-wrapper {
          display: flex;
          align-items: center;
          gap: 6px;

          .app-type-icon {
            font-size: 18px;
            color: #409eff;
          }

          .app-name {
            font-size: 18px;
            font-weight: 700;
            color: #222;
            margin: 0;
          }
        }

        .favorite-icon {
          cursor: pointer;
          font-size: 20px;
          color: #bbb;
          transition: color 0.2s, transform 0.2s;

          &:hover,
          &.is-favorite {
            color: #f7ba2a;
            transform: scale(1.2);
          }
        }
      }

      .app-info {
        font-size: 13px;
        color: #666;
        margin-bottom: 8px;

        .app-id,
        .app-project,
        .app-version {
          margin-bottom: 2px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .app-status-bar {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        gap: 8px;

        .el-tag {
          border-radius: 8px;
          font-size: 12px;
          padding: 2px 10px;
          font-weight: 500;
        }
      }

      .app-footer {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 10px;
        border-top: 1px solid #f2f3f5;
        padding-top: 10px;

        .operations-btn {
          min-width: 80px;
          border-radius: 6px;
          font-weight: 500;
        }

        .el-button--primary,
        .el-button--success {
          font-weight: 600;
          border-radius: 6px;
        }
      }
    }
  }

  .table-card {
    margin-bottom: 20px;

    .app-name-cell {
      display: flex;
      align-items: center;

      .app-type-icon {
        margin-right: 6px;
      }

      .health-dot-small {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-left: 6px;

        &.health-healthy {
          background-color: var(--el-color-success);
        }

        &.health-warning {
          background-color: var(--el-color-warning);
        }

        &.health-error {
          background-color: var(--el-color-danger);
        }

        &.health-unknown {
          background-color: var(--el-text-color-secondary);
        }
      }
    }

    .favorite-icon {
      cursor: pointer;
      font-size: 16px;
      color: var(--el-text-color-secondary);

      &.is-favorite {
        color: #f7ba2a;
      }
    }
  }

  .pagination-wrapper {
    margin-top: 18px;
    text-align: right;

    .el-pagination {
      .el-input__inner {
        min-width: 36px;
        padding: 0 4px;
      }
    }
  }

  .app-detail-container {
    padding: 16px;

    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .detail-title {
        display: flex;
        align-items: center;

        h2 {
          margin: 0;
        }
      }
    }

    .health-status {
      display: flex;
      align-items: center;

      .health-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 6px;

        &.health-healthy {
          background-color: var(--el-color-success);
        }

        &.health-warning {
          background-color: var(--el-color-warning);
        }

        &.health-error {
          background-color: var(--el-color-danger);
        }

        &.health-unknown {
          background-color: var(--el-text-color-secondary);
        }
      }
    }

    .deployment-history {
      margin-top: 24px;

      h3 {
        margin-bottom: 16px;
      }
    }
  }
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}
</style>