<template>
  <el-drawer
    :model-value="visible"
    :title="isEdit ? '编辑微应用' : '创建微应用'"
    size="calc(100% - 240px)"
    @update:model-value="$emit('update:visible', $event)"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="app-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="应用编码" prop="app_code">
            <el-input v-model="form.app_code" @blur="handleRemoteRepos(form.app_code)" placeholder="请输入应用编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应用名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入应用名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="仓库地址" prop="repo_setings">
        <el-select 
          v-model="form.repo_settings" 
          placeholder="请选择仓库地址"
          clearable 
          filterable 
          remote 
          reserve-keyword 
          :remote-method="handleRemoteRepos" 
          :loading="gitLoading" 
          @change="loadScanBranches"
          default-first-option
          style="width: 100%"
          value-key="id"
        >
          <el-option 
            v-for="item in gitOptions" 
            :key="item.id" 
            :label="item.http_url_to_repo || item.name" 
            :value="item" 
          />
        </el-select>
      </el-form-item>

      <el-form-item label="扫描分支" prop="scan_branch">
        <el-select v-model="form.scan_branch" placeholder="请选择扫描分支" @change="loadScanBranches">
          <el-option-group
            v-for="group in scanBranchOptions"
            :key="group.label"
            :label="group.label"
          >
            <el-option
              v-for="item in group.options"
              :key="item.uid"
              :label="item.name"
              :value="item.name"
            />
          </el-option-group>
          <!-- <el-option v-for="item in scanBranchOptions" :key="item.id" :label="item.name" :value="item.name" /> -->
        </el-select>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="应用类型" prop="category">
            <el-select v-model="form.category" placeholder="请选择应用类型">
              <el-option label="前端应用" value="frontend" />
              <el-option label="后端服务" value="backend" />
              <el-option label="中间件" value="middleware" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开发语言" prop="language_code">
            <el-select v-model="form.language_code" placeholder="请选择开发语言">
              <el-option v-for="item in languageOptions" :key="item.id" :label="item.name" :value="item.language_code" />
            </el-select>
          </el-form-item>
        </el-col>
        
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属产品" prop="product_id">
            <el-select v-model="form.product_id" placeholder="请选择产品" @change="loadProjects">
              <el-option
                v-for="product in productOptions"
                :key="product.id"
                :label="product.name"
                :value="product.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属项目" prop="project_id">
            <el-select v-model="form.project_id" placeholder="请选择项目">
              <el-option
                v-for="project in projectOptions"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="Dockerfile" prop="docker_settings.mode">
        <el-radio-group v-model="form.docker_settings.mode">
          <el-radio label="默认" value="default" />
          <el-radio label="项目Dockerfile" value="project" />
          <el-radio label="自定义" value="custom" />
        </el-radio-group>
        <el-input v-if="form.docker_settings.mode === 'custom'" type="textarea" v-model="form.docker_settings.dockerfile" placeholder="请输入Dockerfile" style="margin-top: 10px;" />
      </el-form-item>

      <el-form-item label="可执行文件" prop="target_settings.mode">
        <el-radio-group v-model="form.target_settings.mode">
          <el-radio label="默认" value="default" />
          <el-radio label="自定义" value="custom" />
        </el-radio-group>
        <el-input v-if="form.target_settings.mode === 'custom'" v-model="form.target_settings.path" placeholder="请输入可执行文件路径" />
      </el-form-item>

      <el-form-item label="部署方式" prop="deployment_type">
        <el-radio-group v-model="form.deployment_type">
          <el-radio value="k8s">Kubernetes</el-radio>
          <el-radio value="docker">Docker</el-radio>
          <el-radio value="nonk8s">非Kubernetes</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="启动命令" prop="start_command">
        <el-input v-model="form.start_command" placeholder="请输入启动命令" />
      </el-form-item>

      <el-form-item label="构建命令" prop="build_command">
        <el-input v-model="form.build_command" placeholder="请输入构建命令" />
      </el-form-item>

      <el-form-item label="应用描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入应用描述"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否多应用" prop="is_multi_app">
            <el-switch v-model="form.is_multi_app" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否启用" prop="enabled">
            <el-switch v-model="form.enabled" />
          </el-form-item>
        </el-col>
      </el-row>


      <!-- <el-form-item label="环境配置" prop="environments">
        <el-select 
          v-model="form.environments" 
          multiple 
          placeholder="请选择环境"
          style="width: 100%"
        >
          <el-option
            v-for="env in environmentOptions"
            :key="env.name"
            :label="env.name"
            :value="env.name"
          />
        </el-select>
      </el-form-item> -->
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { gitApi, languageApi, productApi, projectApi } from '../../../api/modules/cmdb'
import { handleAPIResponse } from '../../../utils/response'
import microappApi, { type MicroApp } from '../../../api/modules/microapp'

interface MicroAppCreateRequest {
  app_code: string
  name: string
  product_id?: number
  project_id?: number
  category_name?: string
  language_code?: string
  build_command?: string
  is_multi_app?: boolean
  enabled?: boolean
  description?: string
  deployment_type?: string
  scan_branch?: string
  repo_settings?: any
  docker_settings?: any
  target_settings?: any
  start_command?: string
}

interface Props {
  visible: boolean
  formData?: MicroApp | null
  isEdit: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const gitLoading = ref(false)

// 表单数据
const form = reactive({
  app_code: '',
  name: '',
  category: '',
  product_id: undefined as number | undefined,
  project_id: undefined as number | undefined,
  repo_settings: null as any,
  language_code: '',
  docker_settings: {
    mode: 'default',
    dockerfile: ''
  },
  target_settings: {
    mode: 'default',
    path: ''
  },
  deployment_type: 'k8s',
  start_command: '',
  build_command: '',
  scan_branch: '',
  is_multi_app: false,
  enabled: true,
  description: '',
  // environments: [] as string[]
})

// 选项数据
const productOptions = ref<any[]>([])
const projectOptions = ref<any[]>([])
const languageOptions = ref<any[]>([])
// const environmentOptions = ref<any[]>([])
const gitOptions = ref<any[]>([])
const scanBranchOptions = ref<any[]>([])

// 表单验证规则
const rules: FormRules = {
  app_code: [
    { required: true, message: '请输入应用ID', trigger: 'blur' },
    { min: 3, max: 50, message: '应用ID长度为3-50个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入应用名称', trigger: 'blur' },
    { min: 2, max: 100, message: '应用名称长度为2-100个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择应用类型', trigger: 'change' }
  ],
  product_id: [
    { required: true, message: '请选择所属产品', trigger: 'change' }
  ],
  project_id: [
    { required: true, message: '请选择所属项目', trigger: 'change' }
  ],
  language_code: [
    { required: true, message: '请选择开发语言', trigger: 'change' }
  ]
}

// 加载扫描分支列表
const loadScanBranches = async () => {
  try {
    const res = await gitApi.getBranches({ project_id: form.repo_settings.id || 0, repo_url: form.repo_settings.http_url_to_repo })
    const result = handleAPIResponse(res) as {items:[], total: number}
    scanBranchOptions.value = result.items || []
  } catch (error) {
    console.error('获取扫描分支列表失败:', error)
  }
}

// 远程搜索仓库
const handleRemoteRepos = async (query: string) => {
  console.log('handleRemoteRepos query ', query)

  if (!form.repo_settings?.id && !query) return
  
  gitLoading.value = true
  try {
    if(query !== '') {
      form.repo_settings = null
    }
    const res = await gitApi.getRepos({ project_id: form.repo_settings?.id, search: query })
    const result = handleAPIResponse(res) as {items:[], total: number}
    console.log(result, 'gitlab=========')
    gitOptions.value = result.items || []
  } catch (error) {
    console.error('获取仓库列表失败:', error)
  } finally {
    gitLoading.value = false
  }
}

// 加载产品列表
const loadProducts = async () => {
  try {
    const res = await productApi.getProducts()
    const result = handleAPIResponse(res) as {items:[], total: number}
    productOptions.value = result.items || []
  } catch (error) {
    console.error('获取产品列表失败:', error)
  }
}

// 加载项目列表
const loadProjects = async () => {
  try {
    const res = await productApi.getProductProjects(form.product_id || 0)
    const result = handleAPIResponse(res) as {items:[], total: number}
    projectOptions.value = result.items || []
    console.log(projectOptions.value, 'projectOptions=========')
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}

// 加载开发语言列表
const loadLanguages = async () => {
  try {
    const res = await languageApi.getLanguages()
    const result = handleAPIResponse(res) as {items:[], total: number}
    languageOptions.value = result.items || []
    console.log(languageOptions.value, 'languageOptions=========')
  } catch (error) {
    console.error('获取开发语言列表失败:', error)
  }
}

// 加载环境列表
// const loadEnvironments = async () => {
//   try {
//     const res = await environmentApi.getEnvironments()
//     const result = handleAPIResponse(res) as {items:[], total: number}
//     environmentOptions.value = result.items || []
//     console.log(environmentOptions.value, 'environmentOptions=========')
//   } catch (error) {
//     console.error('获取环境列表失败:', error)
//   }
// }

// 监听表单数据变化
watch(() => props.formData, (newData) => {
  if (newData && props.visible) {
    Object.assign(form, {
      app_code: newData.app_code || '',
      name: newData.name || '',
      category: newData.category_name || '',
      product_id: newData.product_id || undefined,
      project_id: newData.project_id || undefined,
      repo_settings: newData.repo_settings || null,
      language_code: newData.language_code || '',
      docker_settings: newData.docker_settings || {},
      target_settings: newData.target_settings || {},
      deployment_type: newData.deployment_type || 'k8s',
      start_command: newData.docker_settings?.start_command || '',
      build_command: newData.build_command || '',
      scan_branch: newData.scan_branch || 'main',
      is_multi_app: newData.is_multi_app || false,
      enabled: newData.enabled || true,
      description: newData.description || '',
      // environments: newData.tag_list || []
    })
  }
}, { immediate: true })

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    if (!props.isEdit) {
      // 重置表单
      Object.assign(form, {
        app_code: '',
        name: '',
        category: '',
        product_id: undefined,
        project_id: undefined,
        repo: null,
        language_code: '',
        docker_settings: {
          mode: 'default',
          dockerfile: ''
        },
        target_settings: {
          mode: 'default',
          path: ''
        },
        deployment_type: 'k8s',
        start_command: '',
        build_command: '',
        scan_branch: 'main',
        is_multi_app: false,
        enabled: true,
        description: '',
        // environments: [],
        // status: 'active'
      })
      nextTick(() => {
        formRef.value?.clearValidate()
      })
    } else {
      // 编辑表单下，赋值git信息
      console.log('props.formData?.repo_settings?.http_url_to_repo ', props.formData?.repo_settings)
      handleRemoteRepos('')
      // gitOptions.value = [props.formData?.repo_settings]
      loadScanBranches()
      loadProducts()
      loadProjects()
    }
    
    // 加载选项数据
    // loadProjects()
    loadLanguages()
    // loadEnvironments()
  }
})

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 构建提交数据
    const submitData: MicroAppCreateRequest = {
      app_code: form.app_code,
      name: form.name,
      product_id: form.product_id,
      project_id: form.project_id,
      category_name: form.category,
      language_code: form.language_code,
      build_command: form.build_command,
      is_multi_app: form.is_multi_app,
      enabled: form.enabled,
      description: form.description,
      deployment_type: form.deployment_type,
      scan_branch: form.scan_branch,
      repo_settings: form.repo_settings,
      docker_settings: {
        mode: form.docker_settings.mode,
        dockerfile: form.docker_settings.dockerfile,
        start_command: form.start_command
      },
      target_settings: {
        mode: form.target_settings.mode,
        path: form.target_settings.path
      }
    }

    if (props.isEdit && props.formData) {
      console.log('submitData ', submitData)
      await microappApi.updateMicroApp(props.formData.id, submitData)
      ElMessage.success('更新成功')
    } else {
      await microappApi.createMicroApp(submitData)
      ElMessage.success('创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
}

// 组件挂载时加载数据
onMounted(() => {
  // loadProjects()
  loadProducts()
  loadLanguages()
  // loadEnvironments()
})

defineOptions({
  name: 'MicroAppForm'
})
</script>

<script lang="ts">
export default {
  name: 'MicroAppForm'
}
</script>

<style lang="scss" scoped>
.app-form {
  .el-select,
  .el-input {
    width: 100%;
  }
}

.dialog-footer {
  text-align: right;
}
</style> 