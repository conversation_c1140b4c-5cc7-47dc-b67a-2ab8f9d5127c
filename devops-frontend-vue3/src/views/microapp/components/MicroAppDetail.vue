<template>
  <el-dialog
    :model-value="visible"
    title="微应用详情"
    size="calc(100% - 240px)"
    @update:model-value="$emit('update:visible', $event)"
  >
    <div v-if="appData" class="app-detail">
      <!-- 基本信息 -->
      <el-card class="detail-card">
        <template #header>
          <h4>基本信息</h4>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="应用ID">{{ appData.app_id }}</el-descriptions-item>
          <el-descriptions-item label="应用名称">{{ appData.name }}</el-descriptions-item>
          <el-descriptions-item label="应用类型">
            <el-tag :type="getCategoryTagType(appData.category)">
              {{ getCategoryLabel(appData.category) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(appData?.status || '')">
              {{ getStatusLabel(appData?.status || '') }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="所属项目" span="2">
            {{ appData.project?.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="Git仓库" span="2">
            <el-link v-if="appData.git_url" :href="appData.git_url" target="_blank" type="primary">
              {{ appData.git_url }}
            </el-link>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="应用描述" span="2">
            {{ appData.description || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 环境配置 -->
      <el-card class="detail-card">
        <template #header>
          <h4>环境配置</h4>
        </template>
        <div class="environments">
          <el-tag
            v-for="env in appData.environments"
            :key="env"
            class="env-tag"
            size="large"
          >
            {{ getEnvLabel(env) }}
          </el-tag>
          <span v-if="!appData.environments?.length" class="empty-text">暂无环境配置</span>
        </div>
      </el-card>

      <!-- 部署信息 -->
      <el-card class="detail-card">
        <template #header>
          <h4>部署信息</h4>
        </template>
        <el-table :data="deployments" stripe>
          <el-table-column prop="environment" label="环境" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ getEnvLabel(row.environment) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="version" label="版本" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'running' ? 'success' : 'danger'" size="small">
                {{ row.status === 'running' ? '运行中' : '已停止' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="instances" label="实例数" width="100" />
          <el-table-column prop="cpu" label="CPU" width="120" />
          <el-table-column prop="memory" label="内存" width="120" />
          <el-table-column prop="updated_at" label="更新时间" min-width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.updated_at) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import type { MicroApp } from '@/api/modules/microapp'

interface Props {
  visible: boolean
  appData?: MicroApp | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'edit', data: MicroApp): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 模拟部署信息
const deployments = ref([
  {
    environment: 'dev',
    version: 'v1.2.0',
    status: 'running',
    instances: 2,
    cpu: '200m',
    memory: '512Mi',
    updated_at: '2024-01-15T10:30:00Z'
  },
  {
    environment: 'prod',
    version: 'v1.1.0',
    status: 'running',
    instances: 5,
    cpu: '500m',
    memory: '1Gi',
    updated_at: '2024-01-14T15:20:00Z'
  }
])

// 格式化时间
const formatDateTime = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

// 获取分类标签类型
const getCategoryTagType = (category: string) => {
  const typeMap: Record<string, string> = {
    frontend: 'primary',
    backend: 'success',
    middleware: 'warning',
    database: 'info'
  }
  return typeMap[category] || ''
}

// 获取分类标签文本
const getCategoryLabel = (category: string) => {
  const labelMap: Record<string, string> = {
    frontend: '前端应用',
    backend: '后端服务',
    middleware: '中间件',
    database: '数据库'
  }
  return labelMap[category] || category
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    inactive: 'danger',
    maintenance: 'warning'
  }
  return typeMap[status] || ''
}

// 获取状态标签文本
const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    active: '正常',
    inactive: '停用',
    maintenance: '维护中'
  }
  return labelMap[status] || status
}

// 获取环境标签文本
const getEnvLabel = (env: string) => {
  const labelMap: Record<string, string> = {
    dev: '开发环境',
    test: '测试环境',
    staging: '预发布环境',
    prod: '生产环境'
  }
  return labelMap[env] || env
}

// 编辑应用
const handleEdit = () => {
  if (props.appData) {
    emit('edit', props.appData)
    emit('update:visible', false)
  }
}
</script>

<style lang="scss" scoped>
.app-detail {
  .detail-card {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h4 {
      margin: 0;
      color: var(--text-primary);
    }
  }
  
  .environments {
    .env-tag {
      margin-right: 12px;
      margin-bottom: 8px;
    }
    
    .empty-text {
      color: var(--text-placeholder);
      font-style: italic;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style> 