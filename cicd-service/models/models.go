package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

// 通用状态常量
const (
	StatusPending  = 0 // 未执行
	StatusSuccess  = 1 // 成功
	StatusFailed   = 2 // 失败
	StatusRunning  = 3 // 执行中
	StatusCanceled = 4 // 已取消
)

// 部署类型常量
const (
	DeployTypeNew      = 0 // 常规部署
	DeployTypeRollback = 2 // 版本回退
)

// BaseModel 所有模型的基础结构
type BaseModel struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// JSONField 定义JSON字段类型，可以存储任意JSON值
type JSONField struct {
	Data interface{}
}

// 实现json.Marshaler接口
func (j JSONField) MarshalJSON() ([]byte, error) {
	if j.Data == nil {
		return []byte("null"), nil
	}
	return json.Marshal(j.Data)
}

// 实现json.Unmarshaler接口
func (j *JSONField) UnmarshalJSON(data []byte) error {
	return json.Unmarshal(data, &j.Data)
}

// 实现driver.Valuer接口，用于数据库写入
func (j JSONField) Value() (driver.Value, error) {
	if j.Data == nil {
		return nil, nil
	}

	// 如果已经是string类型，直接返回
	if str, ok := j.Data.(string); ok {
		// 检查是否是有效的JSON
		var temp interface{}
		if json.Unmarshal([]byte(str), &temp) == nil {
			return str, nil
		}
		// 如果不是有效JSON，则序列化为JSON字符串
		bytes, err := json.Marshal(str)
		if err != nil {
			return nil, nil
		}
		return string(bytes), nil
	}

	// 序列化为JSON字符串
	bytes, err := json.Marshal(j.Data)
	if err != nil {
		return nil, nil
	}

	return string(bytes), nil
}

// 实现sql.Scanner接口，用于数据库读取
func (j *JSONField) Scan(value interface{}) error {
	if value == nil {
		j.Data = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		if len(v) == 0 {
			j.Data = nil
			return nil
		}
		bytes = v
	case string:
		if v == "" || v == "null" {
			j.Data = nil
			return nil
		}
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into JSONField", value)
	}

	// 尝试解析JSON
	var temp interface{}
	if err := json.Unmarshal(bytes, &temp); err != nil {
		// 如果解析失败，直接存储原始字符串
		j.Data = string(bytes)
		return nil
	}

	j.Data = temp
	return nil
}

// DevLanguage 开发语言模型
type DevLanguage struct {
	BaseModel
	LanguageCode   string    `json:"language_code" gorm:"size:100;uniqueIndex;not null;comment:语言代码"`
	Name           string    `json:"name" gorm:"size:100;uniqueIndex;not null;comment:语言名称"`
	BaseImage      JSONField `json:"base_image" gorm:"type:jsonb;comment:基础镜像配置"`
	BuildSettings  JSONField `json:"build_settings" gorm:"type:jsonb;comment:构建配置"`
	PipelineID     int       `json:"pipeline_id" gorm:"default:0;comment:流水线ID"` // 流水线ID，具体配置在流水线表中
	DockerTemplate string    `json:"docker_template" gorm:"type:text;comment:Dockerfile模板"`
	K8sTemplate    string    `json:"k8s_template" gorm:"type:text;comment:K8s模板"`
	Labels         JSONField `json:"labels" gorm:"type:jsonb;comment:标签"`
	Description    string    `json:"description" gorm:"type:text;comment:描述"`
	SortOrder      int       `json:"sort_order" gorm:"default:999;comment:排序"`
}

func (DevLanguage) TableName() string {
	return "cmdb_dev_languages"
}

// BuildJob 构建任务模型
type BuildJob struct {
	BaseModel
	OrderID     string       `json:"order_id" gorm:"index"`
	AppID       string       `json:"appid" gorm:"column:appid;index"`
	AppInfoID   uint         `json:"app_info_id" gorm:"column:app_info_id;index"`
	DeployerID  uint         `json:"deployer_id" gorm:"column:deployer_id"`
	Deployer    *UserProfile `json:"deployer" gorm:"foreignKey:DeployerID"` // 关联UserProfile
	Status      int          `json:"status" gorm:"default:3"`               // 状态：0-未构建，1-构建成功，2-构建失败，3-构建中，4-已取消
	QueueNumber int          `json:"queue_number"`
	BuildNumber int          `json:"build_number"`
	Commits     string       `json:"commits" gorm:"type:jsonb"`
	CommitTag   string       `json:"commit_tag" gorm:"type:jsonb"`
	IsDeploy    int          `json:"is_deploy" gorm:"default:0"` // 0-仅构建，1-构建并部署
	Image       string       `json:"image"`
	SyncStatus  int          `json:"sync_status" gorm:"default:0"`
	Modules     string       `json:"modules"`
	BatchUUID   string       `json:"batch_uuid"`
}

// TableName 指定BuildJob的表名
func (BuildJob) TableName() string {
	return "deploy_build_jobs"
}

// BuildJobResult 构建结果模型
type BuildJobResult struct {
	BaseModel
	JobID         uint   `json:"job_id" gorm:"index"`
	Result        string `json:"result" gorm:"type:jsonb"`
	ConsoleOutput string `json:"console_output" gorm:"type:text"`
}

// TableName 指定BuildJobResult的表名
func (BuildJobResult) TableName() string {
	return "deploy_build_job_results"
}

// DeployJob 部署任务模型
type DeployJob struct {
	BaseModel
	UniqID          string       `json:"uniq_id" gorm:"uniqueIndex"`
	OrderID         string       `json:"order_id" gorm:"index"`
	AppID           string       `json:"appid" gorm:"column:appid;index"`
	AppInfoID       uint         `json:"app_info_id" gorm:"column:app_info_id;index"`
	DeployerID      uint         `json:"deployer_id"`
	Deployer        *UserProfile `json:"deployer" gorm:"foreignKey:DeployerID"` // 关联UserProfile
	Status          int          `json:"status" gorm:"default:0"`               // 状态：0-未部署，1-部署成功，2-部署失败，3-部署中，4-已取消
	Image           string       `json:"image"`
	Kubernetes      string       `json:"kubernetes" gorm:"type:jsonb"`
	DeployType      int          `json:"deploy_type" gorm:"default:0"` // 0-常规部署，2-版本回退
	RollbackReason  int          `json:"rollback_reason"`
	RollbackComment string       `json:"rollback_comment" gorm:"type:text"`
	Modules         string       `json:"modules"`
	BatchUUID       string       `json:"batch_uuid"`
}

// TableName 指定DeployJob的表名
func (DeployJob) TableName() string {
	return "deploy_deploy_jobs"
}

// DeployJobResult 部署结果模型
type DeployJobResult struct {
	BaseModel
	JobID  uint   `json:"job_id" gorm:"index"`
	Result string `json:"result" gorm:"type:jsonb"`
}

// TableName 指定DeployJobResult的表名
func (DeployJobResult) TableName() string {
	return "deploy_deploy_job_results"
}

// PublishApp 待发布应用模型
type PublishApp struct {
	BaseModel
	OrderID       string `json:"order_id" gorm:"index"`
	AppID         string `json:"appid" gorm:"column:appid;index"`
	AppInfoID     uint   `json:"app_info_id" gorm:"index"`
	Name          string `json:"name"`
	Alias         string `json:"alias"`
	Project       string `json:"project"`
	Product       string `json:"product"`
	Category      string `json:"category"`
	Environment   int    `json:"environment"`
	Branch        string `json:"branch"`
	Image         string `json:"image"`
	Commits       string `json:"commits" gorm:"type:jsonb"`
	DeployType    string `json:"deploy_type"`
	DeployTypeTag int    `json:"deploy_type_tag" gorm:"default:0"`
	Status        int    `json:"status" gorm:"default:0"` // 状态：0-未发布，1-发布成功，2-发布失败，3-发布中，4-已取消
	DeleteFlag    bool   `json:"delete_flag" gorm:"default:false"`
	Modules       string `json:"modules"`
}

// TableName 指定PublishApp的表名
func (PublishApp) TableName() string {
	return "deploy_publish_apps"
}

// PublishOrder 发布工单模型
type PublishOrder struct {
	BaseModel
	OrderID            string    `json:"order_id" gorm:"uniqueIndex"`
	DingTalkTID        string    `json:"dingtalk_tid"`
	Title              string    `json:"title"`
	Category           int       `json:"category" gorm:"default:0"` // 发布类型
	CreatorID          uint      `json:"creator_id"`
	NodeName           string    `json:"node_name"`
	Content            string    `json:"content" gorm:"type:text"`
	FormData           string    `json:"formdata" gorm:"type:jsonb"`
	Effect             string    `json:"effect" gorm:"type:text"`
	Environment        int       `json:"environment"`
	App                string    `json:"app" gorm:"type:jsonb"`
	Status             int       `json:"status" gorm:"default:0"` // 状态：0-未发布，1-发布成功，2-发布失败，3-发布中，4-已取消
	Result             string    `json:"result" gorm:"type:text"`
	ExpectTime         time.Time `json:"expect_time"`
	ExecutorID         uint      `json:"executor_id"`
	DeployTime         time.Time `json:"deploy_time"`
	Method             string    `json:"method" gorm:"default:manual"`
	TeamMembers        string    `json:"team_members" gorm:"type:jsonb"`
	ExtraDeployMembers string    `json:"extra_deploy_members" gorm:"type:jsonb"`
}

// TableName 指定PublishOrder的表名
func (PublishOrder) TableName() string {
	return "deploy_publish_orders"
}

// AppInfo 应用信息模型
type AppInfo struct {
	BaseModel
	UniqueTag      string       `json:"unique_tag" gorm:"size:128;uniqueIndex;not null;comment:唯一标识"`
	AppID          *uint        `json:"app_id" gorm:"comment:应用ID"`
	App            *MicroApp    `json:"app,omitempty" gorm:"foreignKey:AppID;constraint:OnDelete:SET NULL"`
	EnvironmentID  *uint        `json:"environment_id" gorm:"comment:环境ID"`
	Environment    *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID;constraint:OnDelete:SET NULL"`
	BranchSettings JSONField    `json:"branch_settings" gorm:"type:jsonb;comment:分支设置"` // {allow_ci: [], allow_cd: [], default: ''}
	BuildCommand   string       `json:"build_command" gorm:"size:250;comment:构建命令"`
	VersionNumber  string       `json:"version_number" gorm:"size:250;comment:版本号"`
	TemplateData   JSONField    `json:"template_data" gorm:"type:jsonb;comment:模板配置"`
	PipelineID     int          `json:"pipeline_id" gorm:"default:0;comment:流水线ID"` // 流水线ID，具体配置在流水线表中，优先级 应用模块 > 应用 > 开发语言
	IsEnabled      int          `json:"is_enabled" gorm:"default:1;comment:是否启用"`
	Description    string       `json:"description" gorm:"type:text;comment:描述"`
	EditPermission JSONField    `json:"edit_permission" gorm:"type:jsonb;comment:编辑权限"`
	OnlineStatus   int          `json:"online_status" gorm:"default:0;comment:在线状态"`
	PortSettings   JSONField    `json:"port_settings" gorm:"type:jsonb;comment:端口配置"`
	// 多对多关系
	KubernetesClusters    []*KubernetesCluster `json:"kubernetes_clusters,omitempty" gorm:"many2many:cmdb_app_cluster_deploys;"`
	KubernetesClusterInfo []*KubernetesCluster `json:"kubernetes_cluster_info,omitempty" gorm:"-"`
	KubernetesClusterIDs  []uint               `json:"kubernetes_cluster_ids" gorm:"-"`
}

// TableName 指定AppInfo的表名
func (AppInfo) TableName() string {
	return "cmdb_app_infos"
}

// GetNamespace 获取应用的Kubernetes命名空间
func (a *AppInfo) GetNamespace() string {
	if a.Environment.ID == 0 || a.App.ID == 0 || a.App.Project.ID == 0 || a.App.Project.Product.ID == 0 {
		return ""
	}
	envName := strings.Replace(a.Environment.Name, "_", "-", -1)
	productName := strings.Replace(a.App.Project.Product.Name, "_", "-", -1)
	return strings.ToLower(fmt.Sprintf("%s-%s", envName, productName))
}

// Pipeline 流水线模型
type Pipeline struct {
	BaseModel
	PipelineConfig JSONField `json:"pipeline_config" gorm:"type:jsonb;comment:流水线配置"`
	Name           string    `json:"name" gorm:"size:128;default:'python';uniqueIndex;not null;comment:流水线名称"`
	PipelineType   string    `json:"pipeline_type" gorm:"size:128;default:'normal';comment:流水线类型"`
}

func (Pipeline) TableName() string {
	return "cmdb_pipelines"
}

// PipelineTask 流水线任务模型
// TaskName和Namespace联合唯一
type PipelineTask struct {
	BaseModel
	Namespace    string    `json:"namespace" gorm:"uniqueIndex:idx_namespace_task_name;size:128;not null;comment:命名空间"`
	TaskName     string    `json:"task_name" gorm:"uniqueIndex:idx_namespace_task_name;size:128;not null;comment:任务名称"`
	DisplayName  string    `json:"display_name" gorm:"size:128;not null;comment:显示名称"`
	Label        string    `json:"label" gorm:"size:128;not null;comment:标签"`
	Description  string    `json:"description" gorm:"type:text;comment:描述"`
	Params       JSONField `json:"params,omitempty" gorm:"type:jsonb;comment:参数"`
	Steps        JSONField `json:"steps,omitempty" gorm:"type:jsonb;comment:步骤"`
	StepTemplate JSONField `json:"step_template,omitempty" gorm:"type:jsonb;comment:步骤模板"`
	Volumes      JSONField `json:"volumes,omitempty" gorm:"type:jsonb;comment:卷"`
	Sidecars     JSONField `json:"sidecars,omitempty" gorm:"type:jsonb;comment:侧车"`
	Workspaces   JSONField `json:"workspaces,omitempty" gorm:"type:jsonb;comment:工作空间"`
	Results      JSONField `json:"results,omitempty" gorm:"type:jsonb;comment:结果"`
	YamlConfig   string    `json:"yaml_config" gorm:"type:text;comment:YAML配置"`
}

func (PipelineTask) TableName() string {
	return "cmdb_pipeline_tasks"
}

// MicroApp 微服务应用模型
type MicroApp struct {
	BaseModel
	AppCode        string    `json:"app_code" gorm:"size:250;uniqueIndex;not null;comment:应用代码"`
	Name           string    `json:"name" gorm:"size:128;not null;comment:应用名称"`
	ProductID      *uint     `json:"product_id" gorm:"comment:所属产品ID"`
	Product        *Product  `json:"product,omitempty" gorm:"foreignKey:ProductID;constraint:OnDelete:SET NULL"`
	ProjectID      *uint     `json:"project_id" gorm:"comment:所属项目ID"`
	Project        *Project  `json:"project,omitempty" gorm:"foreignKey:ProjectID;constraint:OnDelete:SET NULL"`
	CreatorID      *uint     `json:"creator_id" gorm:"comment:创建人ID"`
	RepoSettings   JSONField `json:"repo_settings" gorm:"type:jsonb;comment:代码仓库配置"`
	TargetSettings JSONField `json:"target_settings" gorm:"type:jsonb;comment:目标配置"`
	TeamMembers    JSONField `json:"team_members" gorm:"type:jsonb;comment:团队成员"`
	CategoryName   string    `json:"category_name" gorm:"size:128;comment:应用分类"`
	TemplateData   JSONField `json:"template_data" gorm:"type:jsonb;comment:应用模板"`
	LanguageCode   string    `json:"language_code" gorm:"size:32;default:'java';comment:开发语言"`
	BuildCommand   string    `json:"build_command" gorm:"size:250;comment:构建命令"`
	IsMultiApp     bool      `json:"is_multi_app" gorm:"default:false;comment:是否多应用"`
	MultiAppIDs    JSONField `json:"multi_app_ids" gorm:"type:jsonb;comment:多应用ID列表"`
	DockerSettings JSONField `json:"docker_settings" gorm:"type:jsonb;comment:Docker配置"`
	Enabled        bool      `json:"enabled" gorm:"default:true;comment:是否启用"`
	Description    string    `json:"description" gorm:"type:text;comment:描述"`
	NotifySettings JSONField `json:"notify_settings" gorm:"type:jsonb;comment:通知配置"`
	EditPermission JSONField `json:"edit_permission" gorm:"type:jsonb;comment:编辑权限"`
	DeploymentType string    `json:"deployment_type" gorm:"size:8;default:'k8s';comment:部署类型"`
	ModuleSettings JSONField `json:"module_settings" gorm:"type:jsonb;comment:模块配置"`
	ScanBranch     string    `json:"scan_branch" gorm:"size:64;default:'test';comment:扫描分支"`
	PipelineID     int       `json:"pipeline_id" gorm:"default:0;comment:流水线ID"` // 流水线ID，具体配置在流水线表中，优先级 应用 > 开发语言
	Tags           []string  `json:"tags" gorm:"type:jsonb;comment:标签"`
	IsFavorite     bool      `json:"is_favorite" gorm:"default:false;comment:是否收藏"`
}

// TableName 指定表名
func (MicroApp) TableName() string {
	return "cmdb_micro_apps"
}

// Region 区域模型
type Region struct {
	BaseModel
	RegionCode  string    `json:"region_code" gorm:"size:100;uniqueIndex;not null;comment:区域代码"`
	Name        string    `json:"name" gorm:"size:100;uniqueIndex;not null;comment:区域名称"`
	Description string    `json:"description" gorm:"type:text;comment:描述"`
	ExtraData   JSONField `json:"extra_data" gorm:"type:jsonb;comment:扩展信息"`
}

// TableName 指定表名
func (Region) TableName() string {
	return "cmdb_regions"
}

// Product 产品模型
type Product struct {
	BaseModel
	ProductCode string    `json:"product_code" gorm:"size:100;uniqueIndex;not null;comment:产品代码"`
	Name        string    `json:"name" gorm:"size:100;uniqueIndex;not null;comment:产品名称"`
	RegionID    *uint     `json:"region_id" gorm:"comment:所属区域ID"`
	Region      *Region   `json:"region,omitempty" gorm:"foreignKey:RegionID;constraint:OnDelete:SET NULL"`
	Description string    `json:"description" gorm:"type:text;comment:描述"`
	NamePrefix  string    `json:"name_prefix" gorm:"size:100;comment:名称前缀"`
	Managers    JSONField `json:"managers" gorm:"type:jsonb;comment:负责人信息"`
}

// TableName 指定表名
func (Product) TableName() string {
	return "cmdb_products"
}

// Project 项目模型
type Project struct {
	BaseModel
	ProjectCode    string    `json:"project_code" gorm:"size:128;uniqueIndex;not null;comment:项目代码"`
	Name           string    `json:"name" gorm:"size:100;not null;comment:项目名称"`
	ProductID      *uint     `json:"product_id" gorm:"comment:所属产品ID"`
	Product        *Product  `json:"product,omitempty" gorm:"foreignKey:ProductID;constraint:OnDelete:SET NULL"`
	CreatorID      *uint     `json:"creator_id" gorm:"comment:创建人ID"`
	Managers       JSONField `json:"managers" gorm:"type:jsonb;comment:负责人信息"` // {manager: '', developer: '', tester: ''}
	Description    string    `json:"description" gorm:"type:text;comment:描述"`
	NotifySettings JSONField `json:"notify_settings" gorm:"type:jsonb;comment:通知配置"`
}

// TableName 指定表名
func (Project) TableName() string {
	return "cmdb_projects"
}

// Environment 环境模型
type Environment struct {
	BaseModel
	EnvironmentCode  string    `json:"environment_code" gorm:"size:100;uniqueIndex;not null;comment:环境代码"`
	Name             string    `json:"name" gorm:"size:100;uniqueIndex;not null;comment:环境名称"`
	TicketEnabled    int       `json:"ticket_enabled" gorm:"default:0;comment:是否启用工单(0-不启用，1-启用)"`
	MergeEnabled     int       `json:"merge_enabled" gorm:"default:0;comment:是否启用合并(0-不启用，1-启用)"`
	TemplateSettings JSONField `json:"template_settings" gorm:"type:jsonb;comment:模板设置"`
	BranchSettings   JSONField `json:"branch_settings" gorm:"type:jsonb;comment:分支设置"` // {allow_ci: [], allow_cd: [], default: ''}
	ExtraData        JSONField `json:"extra_data" gorm:"type:jsonb;comment:扩展信息"`
	Description      string    `json:"description" gorm:"type:text;comment:描述"`
	SortOrder        int       `json:"sort_order" gorm:"default:999;comment:排序"` // 排序
}

// TableName 指定表名
func (Environment) TableName() string {
	return "cmdb_environments"
}

// KubernetesCluster Kubernetes集群模型
type KubernetesCluster struct {
	BaseModel
	ClusterCode  string    `json:"cluster_code" gorm:"size:100;uniqueIndex;not null;comment:集群代码"`
	Name         string    `json:"name" gorm:"size:100;uniqueIndex;not null;comment:集群名称"`
	VersionInfo  JSONField `json:"version_info" gorm:"type:jsonb;comment:版本信息"`
	Description  string    `json:"description" gorm:"type:text;comment:描述"`
	ConfigData   JSONField `json:"config_data" gorm:"type:jsonb;comment:集群配置(加密存储)"`
	DataCenterID *uint     `json:"data_center_id" gorm:"comment:所属数据中心ID"` // 关联数据中心表
	ClusterType  string    `json:"cluster_type" gorm:"size:100;default:'normal';comment:集群类型"`
	ExtraData    JSONField `json:"extra_data" gorm:"type:jsonb;comment:扩展信息"`
	SortOrder    int       `json:"sort_order" gorm:"default:999;comment:排序"`
	// 多对多关系字段
	Environments []*Environment `json:"environments,omitempty" gorm:"many2many:cmdb_cluster_environments;foreignKey:ID;joinForeignKey:cluster_id;References:ID;joinReferences:environment_id"`
	Products     []*Product     `json:"products,omitempty" gorm:"many2many:cmdb_cluster_products;foreignKey:ID;joinForeignKey:cluster_id;References:ID;joinReferences:product_id"`
}

// TableName 指定表名
func (KubernetesCluster) TableName() string {
	return "cmdb_kubernetes_clusters"
}

// AppClusterDeploy 应用集群部署关联模型
type AppClusterDeploy struct {
	BaseModel
	AppInfoID     uint              `json:"app_info_id" gorm:"index;uniqueIndex:idx_app_cluster;comment:应用信息ID"`
	AppInfo       AppInfo           `json:"app_info" gorm:"foreignKey:AppInfoID;constraint:OnDelete:CASCADE"`
	ClusterID     uint              `json:"cluster_id" gorm:"index;uniqueIndex:idx_app_cluster;comment:集群ID"`
	Cluster       KubernetesCluster `json:"cluster" gorm:"foreignKey:ClusterID;constraint:OnDelete:CASCADE"`
	OnlineStatus  int               `json:"online_status" gorm:"default:0;comment:在线状态"`
	VersionNumber string            `json:"version_number" gorm:"size:250;comment:版本号"`
}

// TableName 指定表名
func (AppClusterDeploy) TableName() string {
	return "cmdb_app_cluster_deploys"
}

// PipelineStatus 流水线状态
type PipelineStatus struct {
	ID         string    `json:"id"`
	Name       string    `json:"name"`
	Status     string    `json:"status"`
	StartTime  time.Time `json:"start_time"`
	EndTime    time.Time `json:"end_time,omitempty"`
	BuildImage string    `json:"build_image,omitempty"`
	DeployInfo string    `json:"deploy_info,omitempty"`
}

// BuildRequest 构建请求
type BuildRequest struct {
	Name        string            `json:"name"`
	GitRepo     string            `json:"git_repo"`
	GitBranch   string            `json:"git_branch"`
	DockerImage string            `json:"docker_image"`
	Params      map[string]string `json:"params"`
}

// DeployRequest 部署请求
type DeployRequest struct {
	Name           string            `json:"name"`
	Namespace      string            `json:"namespace"`
	DockerImage    string            `json:"docker_image"`
	DeploymentType string            `json:"deployment_type"`
	Replicas       int               `json:"replicas"`
	Env            map[string]string `json:"env"`
}

// DockerDeploymentParams Docker部署参数
type DockerDeploymentParams struct {
	Name           string            `json:"name"`
	DockerImage    string            `json:"docker_image"`
	Env            map[string]string `json:"env"`
	Replicas       int               `json:"replicas"`
	ExposedPort    int               `json:"exposed_port"`
	TargetHosts    []string          `json:"target_hosts"`
	HarborUsername string            `json:"harbor_username"`
	HarborPassword string            `json:"harbor_password"`
}

// CommitInfo 提交信息
type CommitInfo struct {
	CommitterName string `json:"committer_name"`
	CommittedDate string `json:"committed_date"`
	ShortID       string `json:"short_id"`
	Message       string `json:"message"`
}

// CommitTag 提交标签
type CommitTag struct {
	Label string `json:"label"`
	Name  string `json:"name"`
}

// CIRequest 持续集成请求
type CIRequest struct {
	IsDeploy  bool       `json:"is_deploy"`  // 是否部署
	Modules   string     `json:"modules"`    // 模块
	ID        uint       `json:"id"`         // 应用ID
	Env       uint       `json:"env"`        // 环境ID
	Force     bool       `json:"force"`      // 是否强制构建
	CommitTag CommitTag  `json:"commit_tag"` // 提交标签
	Commits   CommitInfo `json:"commits"`    // 提交信息
	BatchUUID string     `json:"batch_uuid"` // 批次UUID
}

// UserProfile 用户模型
type UserProfile struct {
	BaseModel
	Username    string `json:"username" gorm:"size:150;uniqueIndex;not null;comment:用户名"`
	FirstName   string `json:"first_name" gorm:"size:150;comment:名"`
	LastName    string `json:"last_name" gorm:"size:150;comment:姓"`
	Email       string `json:"email" gorm:"size:254;comment:邮箱"`
	Position    string `json:"position" gorm:"size:100;comment:职位"`
	JobTitle    string `json:"job_title" gorm:"size:100;comment:职称"`
	Mobile      string `json:"mobile" gorm:"size:20;comment:手机号"`
	IsActive    bool   `json:"is_active" gorm:"default:true;comment:是否激活"`
	IsSuperuser bool   `json:"is_superuser" gorm:"default:false;comment:是否超级用户"`
}

// TableName 指定表名
func (UserProfile) TableName() string {
	return "ucenter_users"
}

// Name 获取用户显示名称
func (u *UserProfile) Name() string {
	if u.FirstName != "" {
		return u.FirstName
	}
	if u.LastName != "" {
		return u.LastName
	}
	return u.Username
}
