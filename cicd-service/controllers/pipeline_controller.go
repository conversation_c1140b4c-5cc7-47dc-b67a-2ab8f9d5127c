package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/devops-microservices/cicd-service/config"
	"github.com/devops-microservices/cicd-service/models"
	"github.com/devops-microservices/cicd-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	tektonv1 "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1"
	"gorm.io/gorm"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type PipelineController struct {
	db          *gorm.DB
	logger      logrus.FieldLogger
	cicdService *services.CICDService
	config      *config.Config
}

func NewPipelineController(db *gorm.DB, logger logrus.FieldLogger, cicdService *services.CICDService, config *config.Config) *PipelineController {
	return &PipelineController{db: db, logger: logger, cicdService: cicdService, config: config}
}

// SyncTasksFromTekton 从Tekton同步所有Task到本地PipelineTask表
// @Summary 同步Tekton任务
// @Description 同步Tekton任务
// @Tags Pipeline
// @Accept json
// @Produce json
// @Success 200 {object} gin.H
// @Router /pipeline/tasks/sync [post]
func (c *PipelineController) SyncTasksFromTekton(ctx *gin.Context) {
	tektonClient := c.cicdService.GetTektonClient()
	tasks, err := tektonClient.PipelineClient.TektonV1().Tasks("default").List(ctx, v1.ListOptions{})
	if err != nil {
		c.logger.Errorf("同步Tekton任务失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "同步Tekton任务失败"})
		return
	}
	var synced, failed int
	for _, t := range tasks.Items {
		fmt.Println("t===============", t)
		label := ""
		if t.Labels != nil {
			if v, ok := t.Labels["app"]; ok {
				label = v
			}
		}
		displayName := ""
		if t.Spec.DisplayName != "" {
			displayName = t.Spec.DisplayName
		}
		description := ""
		if t.Spec.Description != "" {
			description = t.Spec.Description
		}
		params := []models.JSONField{}
		for _, p := range t.Spec.Params {
			params = append(params, models.JSONField(p))
		}
		steps := []models.JSONField{}
		for _, s := range t.Spec.Steps {
			steps = append(steps, models.JSONField(s))
		}
		volumes := []models.JSONField{}
		for _, v := range t.Spec.Volumes {
			volumes = append(volumes, models.JSONField(v))
		}
		sidecars := []models.JSONField{}
		for _, s := range t.Spec.Sidecars {
			sidecars = append(sidecars, models.JSONField(s))
		}
		workspaces := []models.JSONField{}
		for _, w := range t.Spec.Workspaces {
			workspaces = append(workspaces, models.JSONField(w))
		}
		results := []models.TaskResult{}
		for _, r := range t.Spec.Results {
			results = append(results, models.JSONField(r))
		}
		stepTemplate := &models.JSONField{}
		if t.Spec.StepTemplate != nil {
			stepTemplate = &models.JSONField(*t.Spec.StepTemplate)
		}
		task := models.PipelineTask{
			Namespace:    t.Namespace,
			TaskName:     t.Name,
			Label:        label,
			DisplayName:  displayName,
			Description:  description,
			Params:       params,
			Steps:        steps,
			Volumes:      volumes,
			Sidecars:     sidecars,
			Workspaces:   workspaces,
			Results:      results,
			StepTemplate: *stepTemplate,
			YamlConfig:   "",
		}
		// upsert: 先查找，存在则更新，否则创建
		var existing models.PipelineTask
		err := c.db.Where("namespace = ? AND task_name = ?", t.Namespace, t.Name).First(&existing).Error
		if err == nil {
			// 存在则更新
			if err := c.db.Model(&existing).Updates(task).Error; err != nil {
				c.logger.Errorf("更新Task失败: %s, 错误: %v, 数据: %+v", t.Name, err, task)
				failed++
				continue
			}
		} else {
			// 不存在则创建
			if err := c.db.Create(&task).Error; err != nil {
				c.logger.Errorf("创建Task失败: %s, 错误: %v, 数据: %+v", t.Name, err, task)
				failed++
				continue
			}
		}
		synced++
	}
	ctx.JSON(http.StatusOK, gin.H{"code": 20000, "message": "同步完成", "synced": synced, "failed": failed})
}

// @Summary 获取任务列表
// @Description 获取任务列表
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param page query int false "页码"
// @Param page_size query int false "每页条数"
// @Success 200 {object} gin.H
// @Router /pipeline/tasks [get]
func (c *PipelineController) GetTasks(ctx *gin.Context) {
	var tasks []models.PipelineTask

	page, _ := strconv.Atoi(ctx.Query("page"))
	pageSize, _ := strconv.Atoi(ctx.Query("page_size"))
	offset := (page - 1) * pageSize
	limit := pageSize

	if err := c.db.Offset(offset).Limit(limit).Find(&tasks).Error; err != nil {
		c.logger.Errorf("获取任务列表失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "获取任务列表失败"})
		return
	}
	var total int64
	c.db.Model(&models.PipelineTask{}).Count(&total)
	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "获取任务列表成功",
		"data": gin.H{
			"items":     tasks,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// @Summary 获取任务详情
// @Description 获取任务详情
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} gin.H
// @Router /pipeline/tasks/{id} [get]
func (c *PipelineController) GetTask(ctx *gin.Context) {
	taskID := ctx.Param("id")
	var task models.PipelineTask
	if err := c.db.First(&task, taskID).Error; err != nil {
		c.logger.Errorf("获取任务详情失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "获取任务详情失败"})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "获取任务详情成功",
		"data":    task,
	})
}

// @Summary 创建任务
// @Description 创建任务
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param task body models.PipelineTask true "任务信息"
// @Success 200 {object} gin.H
// @Router /pipeline/tasks [post]
func (c *PipelineController) CreateTask(ctx *gin.Context) {
	var task models.PipelineTask
	if err := ctx.ShouldBindJSON(&task); err != nil {
		c.logger.Errorf("创建任务失败: %v", err)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "创建任务失败"})
		return
	}
	// 解析 sync_tekton 参数
	syncTekton := true
	if v := ctx.Query("sync_tekton"); v != "" {
		syncTekton = v == "true" || v == "1"
	} else {
		var body map[string]interface{}
		if err := ctx.ShouldBindJSON(&body); err == nil {
			if val, ok := body["sync_tekton"]; ok {
				if b, ok := val.(bool); ok {
					syncTekton = b
				}
			}
		}
	}

	if err := c.db.Create(&task).Error; err != nil {
		c.logger.Errorf("创建任务失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "创建任务失败"})
		return
	}

	// 是否同步到 Tekton
	if syncTekton {
		tektonTask := &tektonv1.Task{
			ObjectMeta: metav1.ObjectMeta{
				Name: task.TaskName,
			},
		}
		tektonClient := c.cicdService.GetTektonClient()
		_, err := tektonClient.PipelineClient.TektonV1().Tasks("default").Create(ctx, tektonTask, metav1.CreateOptions{})
		if err != nil {
			c.logger.Errorf("创建Tekton任务失败: %v", err)
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "创建Tekton任务失败"})
			return
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":        20000,
		"message":     "创建任务成功",
		"data":        task,
		"sync_tekton": syncTekton,
	})
}

// @Summary 更新任务
// @Description 更新任务
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Param task body models.PipelineTask true "任务信息"
// @Success 200 {object} gin.H
// @Router /pipeline/tasks/{id} [put]
func (c *PipelineController) UpdateTask(ctx *gin.Context) {
	taskID := ctx.Param("id")
	var task models.PipelineTask
	if err := ctx.ShouldBindJSON(&task); err != nil {
		c.logger.Errorf("更新任务失败: %v", err)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "更新任务失败"})
		return
	}
	// 解析 sync_tekton 参数
	syncTekton := true
	if v := ctx.Query("sync_tekton"); v != "" {
		syncTekton = v == "true" || v == "1"
	} else {
		var body map[string]interface{}
		if err := ctx.ShouldBindJSON(&body); err == nil {
			if val, ok := body["sync_tekton"]; ok {
				if b, ok := val.(bool); ok {
					syncTekton = b
				}
			}
		}
	}

	if err := c.db.Model(&models.PipelineTask{}).Where("id = ?", taskID).Updates(&task).Error; err != nil {
		c.logger.Errorf("更新任务失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "更新任务失败"})
		return
	}

	if syncTekton {
		tektonTask := &tektonv1.Task{
			ObjectMeta: metav1.ObjectMeta{
				Name: task.TaskName,
			},
		}
		tektonClient := c.cicdService.GetTektonClient()
		_, err := tektonClient.PipelineClient.TektonV1().Tasks("default").Update(ctx, tektonTask, metav1.UpdateOptions{})
		if err != nil {
			c.logger.Errorf("更新Tekton任务失败: %v", err)
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "更新Tekton任务失败"})
			return
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":        20000,
		"message":     "更新任务成功",
		"data":        task,
		"sync_tekton": syncTekton,
	})
}

// @Summary 删除任务
// @Description 删除任务
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} gin.H
// @Router /pipeline/tasks/{id} [delete]
func (c *PipelineController) DeleteTask(ctx *gin.Context) {
	taskID := ctx.Param("id")
	var task models.PipelineTask
	if err := c.db.First(&task, taskID).Error; err != nil {
		c.logger.Errorf("删除任务失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "删除任务失败"})
		return
	}
	if err := c.db.Delete(&models.PipelineTask{}, taskID).Error; err != nil {
		c.logger.Errorf("删除任务失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "删除任务失败"})
		return
	}
	// 解析 sync_tekton 参数
	syncTekton := true
	if v := ctx.Query("sync_tekton"); v != "" {
		syncTekton = v == "true" || v == "1"
	} else {
		var body map[string]interface{}
		if err := ctx.ShouldBindJSON(&body); err == nil {
			if val, ok := body["sync_tekton"]; ok {
				if b, ok := val.(bool); ok {
					syncTekton = b
				}
			}
		}
	}

	if syncTekton {
		tektonClient := c.cicdService.GetTektonClient()
		err := tektonClient.PipelineClient.TektonV1().Tasks("default").Delete(ctx, task.TaskName, metav1.DeleteOptions{})
		if err != nil {
			c.logger.Errorf("删除Tekton任务失败: %v", err)
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "删除Tekton任务失败"})
			return
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":        20000,
		"message":     "删除任务成功",
		"sync_tekton": syncTekton,
	})
}
