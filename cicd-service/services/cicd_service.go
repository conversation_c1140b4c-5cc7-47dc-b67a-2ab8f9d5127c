package services

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/devops-microservices/cicd-service/models"
	"github.com/nats-io/nats.go"
	"github.com/sirupsen/logrus"
	pod "github.com/tektoncd/pipeline/pkg/apis/pipeline/pod"
	tektonv1 "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1"
	"gorm.io/gorm"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8stypes "k8s.io/apimachinery/pkg/types"
)

// CICDService 提供CI/CD服务
type CICDService struct {
	tektonClient     *TektonClientImpl
	kubernetesClient *K8sClient
	ansibleService   *AnsibleService
	pipelineService  *PipelineService
	natsConn         *nats.Conn
	natsSubject      string
	logger           logrus.FieldLogger
	db               *gorm.DB
	harborURL        string // Harbor URL配置字段
	harborUsername   string // Harbor用户名
	harborPassword   string // Harbor密码
	gitlabURL        string // GitLab URL配置字段
	gitlabUsername   string // GitLab用户名
	gitlabToken      string // GitLab Token配置字段
}

// Config 获取TektonClient配置
func (t *TektonClientImpl) Config() *TektonConfig {
	return &TektonConfig{
		Namespace:    "default",
		PipelineName: "build-pipeline",
	}
}

// TektonConfig Tekton配置
type TektonConfig struct {
	Namespace    string // Tekton命名空间
	PipelineName string // Pipeline名称
}

// NewCICDService 创建一个新的CICD服务
func NewCICDService(tektonClient *TektonClientImpl, kubernetesClient *K8sClient,
	natsConn *nats.Conn, natsSubject string, logger logrus.FieldLogger, db *gorm.DB) *CICDService {
	// 创建Ansible服务
	ansibleConfig := &AnsibleConfig{
		AnsiblePath:  "",
		PlaybookDir:  "",
		InventoryDir: "",
	}
	ansibleService := NewAnsibleService(logger, ansibleConfig)

	// 创建Pipeline服务
	pipelineService := NewPipelineService(db, logger)

	return &CICDService{
		tektonClient:     tektonClient,
		kubernetesClient: kubernetesClient,
		ansibleService:   ansibleService,
		pipelineService:  pipelineService,
		natsConn:         natsConn,
		natsSubject:      natsSubject,
		logger:           logger,
		db:               db,
		harborURL:        "harbor.fundpark.com", // 设置默认值，这将在SetConfig方法中更新
		harborUsername:   "",                    // 设置默认值
		harborPassword:   "",                    // 设置默认值
		gitlabURL:        "",                    // 设置默认值
		gitlabUsername:   "",                    // 设置默认值
		gitlabToken:      "",                    // 设置默认值
	}
}

// SetConfig 设置服务的配置信息
func (s *CICDService) SetConfig(harborURL string, harborUsername string, harborPassword string) {
	if harborURL != "" {
		s.harborURL = harborURL
	}
	// 确保harborURL不以/结尾
	s.harborURL = strings.TrimSuffix(s.harborURL, "/")
	s.logger.Infof("Harbor URL 配置设置为: %s", s.harborURL)

	// 设置Harbor认证信息
	if harborUsername != "" {
		s.harborUsername = harborUsername
		s.logger.Infof("Harbor Username 已配置")
	}

	if harborPassword != "" {
		s.harborPassword = harborPassword
		s.logger.Infof("Harbor Password 已配置")
	}
}

// SetGitlabConfig 设置GitLab配置信息
func (s *CICDService) SetGitlabConfig(gitlabURL string, gitlabToken string, gitlabUsername string) {
	s.gitlabURL = gitlabURL
	s.gitlabToken = gitlabToken
	s.gitlabUsername = gitlabUsername

	if s.gitlabURL != "" {
		s.logger.Infof("GitLab URL 配置设置为: %s", s.gitlabURL)
	}

	if s.gitlabUsername != "" {
		s.logger.Infof("GitLab Username 已配置")
	}

	if s.gitlabToken != "" {
		s.logger.Info("GitLab Token 已配置")
	}
}

// publishEvent 发布事件到NATS
func (s *CICDService) publishEvent(eventType string, payload map[string]interface{}) {
	if s.natsConn == nil {
		s.logger.Debug("NATS连接不可用，跳过事件发布")
		return
	}

	// 确保subject不以点结尾
	cleanSubject := strings.TrimSuffix(s.natsSubject, ".")
	subject := fmt.Sprintf("%s.%s", cleanSubject, eventType)

	data, err := json.Marshal(payload)
	if err != nil {
		s.logger.Errorf("事件序列化失败: %v", err)
		return
	}

	s.logger.Debugf("发布事件到 %s: %s", subject, string(data))
	if err := s.natsConn.Publish(subject, data); err != nil {
		s.logger.Errorf("发布事件失败: %v", err)
	}
}

// GetAppInfo 获取AppInfo信息
func (s *CICDService) GetAppInfo(ctx context.Context, appinfoID string) (*models.AppInfo, error) {
	id := 0
	fmt.Sscanf(appinfoID, "%d", &id)
	if id <= 0 {
		return nil, fmt.Errorf("无效的app_info_id: %s", appinfoID)
	}

	var appInfo models.AppInfo
	s.logger.Infof("从表 %s 查询 AppInfo: ID=%d", appInfo.TableName(), id)

	if err := s.db.First(&appInfo, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			s.logger.Warnf("未找到AppInfo记录(ID=%s): %v", appinfoID, err)
			return nil, fmt.Errorf("未找到AppInfo记录: %s", appinfoID)
		}
		s.logger.Errorf("查询AppInfo(ID=%s)失败: %v", appinfoID, err)
		return nil, fmt.Errorf("查询AppInfo失败: %v", err)
	}

	s.logger.Infof("成功获取AppInfo: ID=%d, UniqueTag=%s", id, appInfo.UniqueTag)
	return &appInfo, nil
}

// GetApp 获取MicroApp信息
func (s *CICDService) GetApp(ctx context.Context, appID int) (*models.MicroApp, error) {
	var microApp models.MicroApp
	s.logger.Infof("从表 %s 查询 MicroApp: ID=%d", microApp.TableName(), appID)

	// 首先获取MicroApp基本信息
	if err := s.db.First(&microApp, appID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			s.logger.Warnf("未找到MicroApp记录(ID=%d): %v", appID, err)
			return nil, fmt.Errorf("未找到MicroApp记录: %d", appID)
		}
		s.logger.Errorf("查询MicroApp(ID=%d)失败: %v", appID, err)
		return nil, fmt.Errorf("查询MicroApp失败: %v", err)
	}

	// 如果ProjectID有值，则加载Project信息
	if *microApp.ProjectID > 0 {
		var project models.Project
		if err := s.db.First(&project, microApp.ProjectID).Error; err != nil {
			s.logger.Warnf("获取Project信息失败(ProjectID=%d): %v", microApp.ProjectID, err)
			// 不返回错误，使用默认值
		} else {
			microApp.Project = &project

			// 如果Project有ProductID，则加载Product信息
			if *project.ProductID > 0 {
				var product models.Product
				if err := s.db.First(&product, project.ProductID).Error; err != nil {
					s.logger.Warnf("获取Product信息失败(ProductID=%d): %v", project.ProductID, err)
					// 不返回错误，使用默认值
				} else {
					microApp.Project.Product = &product
				}
			}
		}
	}

	s.logger.Infof("成功获取MicroApp: ID=%d, Name=%s, AppID=%s", appID, microApp.Name, microApp.ID)
	return &microApp, nil
}

// ensureHarborURLPrefix 确保镜像名称包含Harbor URL前缀（不包含协议）
func (s *CICDService) ensureHarborURLPrefix(image string) string {
	// 清理Harbor URL，移除协议前缀
	harborHost := s.harborURL
	harborHost = strings.TrimPrefix(harborHost, "http://")
	harborHost = strings.TrimPrefix(harborHost, "https://")
	harborHost = strings.TrimSuffix(harborHost, "/")

	// 检查是否已包含Registry URL
	if !strings.Contains(image, "/") || !strings.Contains(strings.Split(image, "/")[0], ".") {
		// 如果镜像名称不包含Harbor URL前缀，则添加
		if !strings.HasPrefix(image, harborHost) {
			image = fmt.Sprintf("%s/%s", harborHost, image)
			s.logger.Infof("添加Harbor URL前缀到镜像名称: %s", image)
		}
	}

	return image
}

// ensureHarborRegistrySecret 确保harbor-registry-config和docker-config Secret在Kubernetes中存在并更新
func (s *CICDService) ensureHarborRegistrySecret(ctx context.Context) error {
	// 生成认证字符串
	authString := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", s.harborUsername, s.harborPassword)))

	// 生成Docker配置JSON
	dockerConfigJSON := fmt.Sprintf(`{
		"auths": {
			"%s": {
				"username": "%s",
				"password": "%s",
				"auth": "%s"
			}
		}
	}`, s.harborURL, s.harborUsername, s.harborPassword, authString)

	// 创建harbor-registry-config Secret
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "harbor-registry-config",
			Namespace: "default", // 使用默认命名空间
		},
		Type: corev1.SecretTypeDockerConfigJson,
		StringData: map[string]string{
			".dockerconfigjson": dockerConfigJSON,
		},
	}

	// 尝试创建Secret
	_, err := s.kubernetesClient.ClientSet.CoreV1().Secrets("default").Create(ctx, secret, metav1.CreateOptions{})
	if err != nil {
		if !k8serrors.IsAlreadyExists(err) {
			return fmt.Errorf("创建harbor-registry-config Secret失败: %v", err)
		}
		s.logger.Info("Harbor Registry Secret已存在")
	} else {
		s.logger.Info("成功创建harbor-registry-config Secret")
	}

	// 创建docker-config Secret用于Tekton
	dockerConfigSecret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "docker-config",
			Namespace: "default", // 使用默认命名空间
		},
		Type: corev1.SecretTypeDockerConfigJson,
		StringData: map[string]string{
			".dockerconfigjson": dockerConfigJSON,
			"config.json":       dockerConfigJSON,
		},
	}

	// 尝试创建Secret
	_, err = s.kubernetesClient.ClientSet.CoreV1().Secrets("default").Create(ctx, dockerConfigSecret, metav1.CreateOptions{})
	if err != nil {
		if !k8serrors.IsAlreadyExists(err) {
			return fmt.Errorf("创建docker-config Secret失败: %v", err)
		}
		s.logger.Info("docker-config Secret已存在")
	} else {
		s.logger.Info("成功创建docker-config Secret")
	}

	return nil
}

// ensureHarborLoginSecret 确保harbor登录Secret在Kubernetes中存在并更新
func (s *CICDService) ensureHarborLoginSecret(ctx context.Context) error {
	// 生成认证字符串
	authString := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", s.harborUsername, s.harborPassword)))

	// 生成Docker配置JSON
	dockerConfigJSON := fmt.Sprintf(`{
		"auths": {
			"%s": {
				"username": "%s",
				"password": "%s",
				"auth": "%s"
			}
		}
	}`, s.harborURL, s.harborUsername, s.harborPassword, authString)

	// 创建新的Secret
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "loginharbor",
			Namespace: "default", // 使用默认命名空间
		},
		Type: corev1.SecretTypeDockerConfigJson,
		StringData: map[string]string{
			".dockerconfigjson": dockerConfigJSON,
		},
	}

	// 尝试创建Secret
	_, err := s.kubernetesClient.ClientSet.CoreV1().Secrets("default").Create(ctx, secret, metav1.CreateOptions{})
	if err != nil {
		if k8serrors.IsAlreadyExists(err) {
			// Secret已存在，无需创建
			s.logger.Info("Harbor Login Secret已存在")
			return nil
		}
		return fmt.Errorf("创建loginharbor Secret失败: %v", err)
	}

	s.logger.Info("成功创建loginharbor Secret")
	return nil
}

// ensureGitlabAuthSecret 确保GitLab认证Secret存在
func (s *CICDService) ensureGitlabAuthSecret(ctx context.Context) error {
	if s.gitlabURL == "" || s.gitlabToken == "" || s.gitlabUsername == "" {
		return fmt.Errorf("GitLab URL、用户名或Token未配置")
	}

	secretName := "gitlab-auth"
	namespace := s.tektonClient.Config().Namespace

	// 清理URL，移除协议部分和尾部斜杠
	cleanURL := s.gitlabURL
	cleanURL = strings.TrimPrefix(cleanURL, "http://")
	cleanURL = strings.TrimPrefix(cleanURL, "https://")
	cleanURL = strings.TrimSuffix(cleanURL, "/")

	// 创建.gitconfig内容
	gitconfigContent := fmt.Sprintf(`[credential]
	helper = store
`)

	// 创建.git-credentials内容
	gitCredentialsContent := fmt.Sprintf("https://%s:%s@%s", s.gitlabUsername, s.gitlabToken, cleanURL)

	s.logger.Infof("创建GitLab认证信息，使用用户名: %s", s.gitlabUsername)

	// 检查Secret是否存在
	_, err := s.kubernetesClient.GetSecret(ctx, secretName, namespace)
	if err == nil {
		// Secret已存在，更新它
		return s.kubernetesClient.UpdateSecret(ctx, secretName, namespace, map[string][]byte{
			".gitconfig":       []byte(gitconfigContent),
			".git-credentials": []byte(gitCredentialsContent),
		})
	}

	// Secret不存在，创建它
	return s.kubernetesClient.CreateSecret(ctx, secretName, namespace, map[string][]byte{
		".gitconfig":       []byte(gitconfigContent),
		".git-credentials": []byte(gitCredentialsContent),
	})
}

// CreateBuild 创建构建任务
func (s *CICDService) CreateBuild(ctx context.Context, req models.BuildRequest) (string, error) {
	s.logger.Infof("创建构建任务: %s", req.Name)

	// 先检查Pipeline是否存在
	_, err := s.tektonClient.PipelineClient.TektonV1().Pipelines("default").Get(ctx, "build-pipeline", metav1.GetOptions{})
	if err != nil {
		return "", fmt.Errorf("检查Pipeline失败: %v", err)
	}

	// 创建 PipelineRun
	pipelineRun := &tektonv1.PipelineRun{
		ObjectMeta: metav1.ObjectMeta{
			GenerateName: "build-",
			Namespace:    "default",
		},
		Spec: tektonv1.PipelineRunSpec{
			PipelineRef: &tektonv1.PipelineRef{
				Name: "build-pipeline",
			},
			Params: []tektonv1.Param{
				{
					Name: "git-url",
					Value: tektonv1.ParamValue{
						Type:      tektonv1.ParamTypeString,
						StringVal: req.GitRepo,
					},
				},
				{
					Name: "git-revision",
					Value: tektonv1.ParamValue{
						Type:      tektonv1.ParamTypeString,
						StringVal: req.GitBranch,
					},
				},
				{
					Name: "image-name",
					Value: tektonv1.ParamValue{
						Type:      tektonv1.ParamTypeString,
						StringVal: req.DockerImage,
					},
				},
			},
			Workspaces: []tektonv1.WorkspaceBinding{
				{
					Name: "shared-workspace",
					PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
						ClaimName: "tekton-workspace-pvc",
					},
				},
				{
					Name: "docker-credentials",
					Secret: &corev1.SecretVolumeSource{
						SecretName: "docker-config",
					},
				},
				{
					Name: "gitlab-credentials",
					Secret: &corev1.SecretVolumeSource{
						SecretName: "gitlab-auth",
					},
				},
			},
			TaskRunTemplate: tektonv1.PipelineTaskRunTemplate{
				ServiceAccountName: "tekton-sa",
				PodTemplate: &pod.Template{
					HostAliases: []corev1.HostAlias{
						{
							IP: "************",
							Hostnames: []string{
								"harbor.fundpark.com",
							},
						},
					},
				},
			},
		},
	}

	// 添加其他参数
	for key, value := range req.Params {
		pipelineRun.Spec.Params = append(pipelineRun.Spec.Params, tektonv1.Param{
			Name: key,
			Value: tektonv1.ParamValue{
				Type:      tektonv1.ParamTypeString,
				StringVal: value,
			},
		})
	}

	// 创建 PipelineRun
	createdPipelineRun, err := s.tektonClient.PipelineClient.TektonV1().PipelineRuns("default").Create(ctx, pipelineRun, metav1.CreateOptions{})
	if err != nil {
		return "", fmt.Errorf("创建 PipelineRun 失败: %v", err)
	}

	return createdPipelineRun.Name, nil
}

// CreateDeploy 创建部署任务
func (s *CICDService) CreateDeploy(ctx context.Context, req models.DeployRequest) (string, error) {
	// 基于部署类型创建相应的部署（Kubernetes或Docker）
	var deployID string
	var err error

	if req.DeploymentType == "kubernetes" {
		deployID, err = s.createKubernetesDeployment(ctx, req)
	} else {
		deployID, err = s.createDockerDeployment(ctx, req)
	}

	if err != nil {
		// 发布部署失败事件
		s.publishEvent("deploy.failed", map[string]interface{}{
			"name":      req.Name,
			"namespace": req.Namespace,
			"type":      req.DeploymentType,
			"error":     err.Error(),
			"timestamp": time.Now(),
		})
		return "", err
	}

	// 发布部署成功事件
	s.publishEvent("deploy.succeeded", map[string]interface{}{
		"deploy_id": deployID,
		"name":      req.Name,
		"namespace": req.Namespace,
		"type":      req.DeploymentType,
		"timestamp": time.Now(),
	})

	return deployID, nil
}

// createKubernetesDeployment 创建Kubernetes部署
func (s *CICDService) createKubernetesDeployment(ctx context.Context, req models.DeployRequest) (string, error) {
	// 确保镜像名称包含Harbor URL前缀
	req.DockerImage = s.ensureHarborURLPrefix(req.DockerImage)

	// 标准标签
	labels := map[string]string{
		"app":         req.Name,
		"environment": req.Namespace, // 使用namespace作为环境名称
	}

	// 环境名称，默认为dev
	env := strings.ToLower(req.Namespace)
	if env == "" {
		env = "dev"
	}

	// 构建环境变量
	envVars := []corev1.EnvVar{
		{
			Name:  "APP_NAME",
			Value: req.Name,
		},
		{
			Name:  "LOG_LEVEL",
			Value: "info",
		},
		{
			Name:  "ENVIRONMENT",
			Value: env,
		},
		{
			Name:  "HARBOR_USERNAME",
			Value: s.harborUsername,
		},
		{
			Name:  "HARBOR_PASSWORD",
			Value: s.harborPassword,
		},
	}

	// 添加自定义环境变量
	for key, value := range req.Env {
		envVars = append(envVars, corev1.EnvVar{
			Name:  key,
			Value: value,
		})
	}

	// 确保Harbor认证Secret存在
	if err := s.ensureHarborRegistrySecret(ctx); err != nil {
		return "", fmt.Errorf("确保Harbor认证Secret失败: %v", err)
	}

	// 创建部署资源的逻辑
	// 这里应该根据应用类型和配置生成适当的Kubernetes资源
	// 本示例仅提供基本结构

	// 生成唯一的部署ID
	deployID := fmt.Sprintf("%s/%s-%s", req.Namespace, req.Name, time.Now().Format("20060102-150405"))

	// 实际部署逻辑将在这里实现
	// 使用labels添加到部署资源上
	s.logger.Infof("为部署 %s 创建资源，使用标签: %v", deployID, labels)

	return deployID, nil
}

// createDockerDeployment 创建Docker部署
func (s *CICDService) createDockerDeployment(ctx context.Context, req models.DeployRequest) (string, error) {
	// 确保镜像名称包含Harbor URL前缀
	req.DockerImage = s.ensureHarborURLPrefix(req.DockerImage)

	// 使用Ansible服务进行Docker部署
	params := &models.DockerDeploymentParams{
		Name:           req.Name,
		DockerImage:    req.DockerImage,
		Env:            req.Env,
		Replicas:       req.Replicas,
		ExposedPort:    8080,                       // 默认端口
		TargetHosts:    []string{"host1", "host2"}, // 应从请求或配置中获取
		HarborUsername: s.harborUsername,
		HarborPassword: s.harborPassword,
	}

	// 调用Ansible服务执行部署
	deploymentID, err := s.ansibleService.DeployDocker(ctx, params)
	if err != nil {
		return "", fmt.Errorf("Docker部署失败: %v", err)
	}

	return deploymentID, nil
}

// ListPipelines 列出所有流水线
func (s *CICDService) ListPipelines(ctx context.Context) ([]models.PipelineStatus, error) {
	s.logger.Info("列出所有流水线")

	// 获取所有PipelineRun
	pipelineRuns, err := s.tektonClient.PipelineClient.TektonV1().PipelineRuns("").List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	// 转换为PipelineStatus
	var pipelines []models.PipelineStatus
	for _, pr := range pipelineRuns.Items {
		status := "running"
		var endTime time.Time

		// 检查状态
		if pr.Status.CompletionTime != nil {
			endTime = pr.Status.CompletionTime.Time
			for _, condition := range pr.Status.Conditions {
				if condition.Type == "Succeeded" {
					status = "succeeded"
					break
				}
			}
		}

		pipeline := models.PipelineStatus{
			ID:        pr.Name,
			Name:      pr.Labels["tekton.dev/pipeline"],
			Status:    status,
			StartTime: pr.Status.StartTime.Time,
			EndTime:   endTime,
		}

		// 尝试获取构建镜像和部署信息
		for _, param := range pr.Spec.Params {
			if param.Name == "image-name" {
				pipeline.BuildImage = param.Value.StringVal
			}
		}

		pipelines = append(pipelines, pipeline)
	}

	return pipelines, nil
}

// GetPipelineStatus 获取流水线状态
func (s *CICDService) GetPipelineStatus(ctx context.Context, pipelineID string) (models.PipelineStatus, error) {
	// 解析出命名空间和名称
	parts := strings.Split(pipelineID, "/")
	namespace := "default"
	name := pipelineID

	if len(parts) == 2 {
		namespace = parts[0]
		name = parts[1]
	}

	// 获取PipelineRun状态
	pipelineRun, err := s.tektonClient.PipelineClient.TektonV1().PipelineRuns(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return models.PipelineStatus{
				ID:     pipelineID,
				Status: "not_found",
			}, nil
		}
		return models.PipelineStatus{}, err
	}

	// 检查PipelineRun状态
	status := "running"
	var endTime time.Time

	if pipelineRun.Status.CompletionTime != nil {
		endTime = pipelineRun.Status.CompletionTime.Time
		for _, condition := range pipelineRun.Status.Conditions {
			if condition.Type == "Succeeded" {
				status = "succeeded"
				break
			}
		}
	}

	// 构建PipelineStatus
	pipelineStatus := models.PipelineStatus{
		ID:        pipelineRun.Name,
		Name:      pipelineRun.Labels["tekton.dev/pipeline"],
		Status:    status,
		StartTime: pipelineRun.Status.StartTime.Time,
		EndTime:   endTime,
	}

	// 尝试获取构建镜像和部署信息
	for _, param := range pipelineRun.Spec.Params {
		if param.Name == "image-name" {
			pipelineStatus.BuildImage = param.Value.StringVal
		}
	}

	return pipelineStatus, nil
}

// GetBuildStatus 获取构建状态
func (s *CICDService) GetBuildStatus(ctx context.Context, buildID string) (string, error) {
	// 解析出命名空间和名称
	parts := strings.Split(buildID, "/")
	namespace := "default"
	name := buildID

	if len(parts) == 2 {
		namespace = parts[0]
		name = parts[1]
	}

	// 获取PipelineRun状态
	pipelineRun, err := s.tektonClient.PipelineClient.TektonV1().PipelineRuns(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return "not_found", nil
		}
		return "unknown", err
	}

	// 检查PipelineRun状态
	if pipelineRun.Status.CompletionTime == nil {
		return "running", nil
	}

	// 检查是否成功
	for _, condition := range pipelineRun.Status.Conditions {
		if condition.Type == "Succeeded" {
			if condition.Status == "True" {
				return "succeeded", nil
			} else {
				return "failed", nil
			}
		}
	}

	return "unknown", nil
}

// GetDeployStatus 获取部署状态
func (s *CICDService) GetDeployStatus(ctx context.Context, deployID string) (map[string]interface{}, error) {
	// 检查是否是Ansible部署的Docker服务
	if strings.HasPrefix(deployID, "ansible-docker-") {
		// 提取服务名称
		serviceName := strings.TrimPrefix(deployID, "ansible-docker-")
		s.logger.Infof("检查Ansible部署的Docker服务状态: %s", serviceName)
		return map[string]interface{}{
			"id":      deployID,
			"name":    serviceName,
			"status":  "succeeded",
			"type":    "ansible-docker",
			"updated": time.Now(),
		}, nil
	}

	// 解析出命名空间和名称
	parts := strings.Split(deployID, "/")
	namespace := "default"
	name := deployID

	if len(parts) == 2 {
		namespace = parts[0]
		name = parts[1]
	}

	// 对于Kubernetes部署
	deployment, err := s.kubernetesClient.ClientSet.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return map[string]interface{}{
				"id":     deployID,
				"status": "not_found",
			}, nil
		}
		return nil, err
	}

	// 获取Pods
	pods, err := s.kubernetesClient.ClientSet.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("app=%s", name),
	})
	if err != nil {
		return nil, err
	}

	// 检查部署状态
	status := "deploying"
	if deployment.Status.ReadyReplicas == deployment.Status.Replicas {
		status = "ready"
	} else if deployment.Status.Replicas == 0 {
		status = "scaled_down"
	}

	// 构建状态信息
	statusInfo := map[string]interface{}{
		"id":              deployID,
		"name":            name,
		"namespace":       namespace,
		"status":          status,
		"type":            "kubernetes",
		"replicas":        deployment.Status.Replicas,
		"ready_replicas":  deployment.Status.ReadyReplicas,
		"updated":         deployment.Status.ObservedGeneration,
		"pod_count":       len(pods.Items),
		"image":           getContainerImage(deployment),
		"last_deployment": deployment.CreationTimestamp.Time,
	}

	return statusInfo, nil
}

// getContainerImage 获取部署中的容器镜像
func getContainerImage(deployment interface{}) string {
	// 这里需要实现获取部署中容器镜像的逻辑
	// 由于部署对象类型是interface{}，需要进行类型断言
	return "placeholder-image"
}

// WaitForBuildCompletion 等待构建完成
func (s *CICDService) WaitForBuildCompletion(ctx context.Context, buildID string) error {
	s.logger.Infof("等待构建 %s 完成", buildID)

	// 解析出命名空间和名称
	// 假设buildID格式为: namespace/name
	parts := strings.Split(buildID, "/")
	namespace := "default"
	name := buildID

	if len(parts) == 2 {
		namespace = parts[0]
		name = parts[1]
	}

	// 发布构建等待事件
	s.publishEvent("build.waiting", map[string]interface{}{
		"build_id":  buildID,
		"timestamp": time.Now(),
	})

	// 轮询检查PipelineRun状态
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	timeout := time.After(60 * time.Minute)

	for {
		select {
		case <-ticker.C:
			// 检查PipelineRun状态
			pipelineRun, err := s.tektonClient.PipelineClient.TektonV1().PipelineRuns(namespace).Get(ctx, name, metav1.GetOptions{})
			if err != nil {
				if k8serrors.IsNotFound(err) {
					s.logger.Warnf("构建 %s 未找到", buildID)
					continue
				}
				s.logger.Errorf("检查构建状态失败: %v", err)
				continue
			}

			// 检查是否完成
			if pipelineRun.Status.CompletionTime != nil {
				s.logger.Infof("构建 %s 已完成", buildID)

				// 检查是否成功
				for _, condition := range pipelineRun.Status.Conditions {
					if condition.Type == "Succeeded" {
						if condition.Status == "True" {
							// 发布构建成功事件
							s.publishEvent("build.succeeded", map[string]interface{}{
								"build_id":  buildID,
								"timestamp": time.Now(),
							})
							return nil
						} else {
							// 发布构建失败事件
							s.publishEvent("build.failed", map[string]interface{}{
								"build_id":  buildID,
								"reason":    condition.Reason,
								"message":   condition.Message,
								"timestamp": time.Now(),
							})
							return fmt.Errorf("构建失败: %s - %s", condition.Reason, condition.Message)
						}
					}
				}
			}

		case <-timeout:
			// 超时
			s.logger.Warnf("等待构建 %s 完成超时", buildID)
			s.publishEvent("build.timeout", map[string]interface{}{
				"build_id":  buildID,
				"timestamp": time.Now(),
			})
			return fmt.Errorf("等待构建完成超时")

		case <-ctx.Done():
			// 上下文取消
			s.logger.Warnf("等待构建 %s 完成被取消", buildID)
			return ctx.Err()
		}
	}
}

// CancelBuild 取消构建
func (s *CICDService) CancelBuild(ctx context.Context, buildID string) error {
	// 解析出命名空间和名称
	parts := strings.Split(buildID, "/")
	namespace := "default"
	name := buildID

	if len(parts) == 2 {
		namespace = parts[0]
		name = parts[1]
	}

	// 为PipelineRun添加取消标记
	patch := []byte(`{"spec":{"status":"Cancelled"}}`)
	_, err := s.tektonClient.PipelineClient.TektonV1().PipelineRuns(namespace).Patch(
		ctx,
		name,
		k8stypes.JSONPatchType,
		patch,
		metav1.PatchOptions{},
	)

	return err
}

// CancelPipeline 取消指定的Pipeline
func (s *CICDService) CancelPipeline(ctx context.Context, id string) error {
	s.logger.Infof("取消Pipeline: %s", id)

	// 解析命名空间和名称
	parts := strings.Split(id, "/")
	namespace := "default"
	name := id

	if len(parts) == 2 {
		namespace = parts[0]
		name = parts[1]
	}

	// 创建PipelineRun取消补丁
	patchBytes := []byte(`{"spec":{"status":"PipelineRunCancelled"}}`)

	// 应用补丁到PipelineRun
	_, err := s.tektonClient.PipelineClient.TektonV1().PipelineRuns(namespace).Patch(
		ctx, name, k8stypes.MergePatchType, patchBytes, metav1.PatchOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			s.logger.Warnf("找不到要取消的Pipeline: %s", id)
			return nil
		}
		return fmt.Errorf("取消Pipeline失败: %v", err)
	}

	s.logger.Infof("成功取消Pipeline: %s", id)
	return nil
}

// GetBuildStartTime 获取构建开始时间
func (s *CICDService) GetBuildStartTime(ctx context.Context, buildID string) (time.Time, error) {
	// 尝试获取真实的开始时间
	pipelineRun, err := s.tektonClient.PipelineClient.TektonV1().PipelineRuns("default").Get(ctx, buildID, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return time.Time{}, fmt.Errorf("构建 %s 未找到", buildID)
		}
		return time.Time{}, err
	}

	if pipelineRun.Status.StartTime != nil {
		return pipelineRun.Status.StartTime.Time, nil
	}

	// 如果没有开始时间，返回创建时间
	return pipelineRun.CreationTimestamp.Time, nil
}

// CancelDeploy 取消部署
func (s *CICDService) CancelDeploy(ctx context.Context, deployID string) error {
	// 调用Ansible服务取消部署
	return s.ansibleService.CancelDeployment(ctx, deployID)
}

// GetTektonClient 返回tekton client
func (s *CICDService) GetTektonClient() *TektonClientImpl {
	return s.tektonClient
}
