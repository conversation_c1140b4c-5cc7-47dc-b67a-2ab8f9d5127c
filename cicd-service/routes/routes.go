package routes

import (
	"sort"
	"strings"

	"github.com/devops-microservices/cicd-service/config"
	"github.com/devops-microservices/cicd-service/controllers"
	"github.com/devops-microservices/cicd-service/middlewares"
	"github.com/devops-microservices/cicd-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// SetupRoutes 设置所有API路由
func SetupRoutes(r *gin.Engine, db *gorm.DB, logger logrus.FieldLogger, cicdService *services.CICDService, publishService *services.PublishService, cfg *config.Config) {
	// 创建控制器
	buildController := controllers.NewBuildController(db, logger)
	deployController := controllers.NewDeployController(db, logger, cicdService, cfg)
	apiController := controllers.NewAPIController(cicdService, publishService, logger, db)
	pipelineController := controllers.NewPipelineController(db, logger, cicdService, cfg)
	// 获取标准logger (转换FieldLogger为*logrus.Logger)
	var standardLogger *logrus.Logger
	if l, ok := logger.(*logrus.Logger); ok {
		standardLogger = l
	} else {
		// 如果不能转换，创建一个新的logger
		standardLogger = logrus.New()
		standardLogger.SetLevel(logrus.InfoLevel)
	}

	// 创建认证中间件
	authMiddleware := middlewares.JWTAuthMiddleware(cfg, standardLogger)

	// 健康检查 - 不需要认证
	r.GET("/health", apiController.HealthCheck)

	// API路由组 - 需要认证
	v1 := r.Group("/api/v1/cicd")
	{
		// 公开API - 不需要认证的路由

		// 需要认证的API路由
		auth := v1.Group("")
		auth.Use(authMiddleware)
		auth.GET("/:build_id/logs", buildController.GetBuildLogs)          // 获取构建日志 - 用于外部系统查看
		auth.GET("/deploy/:build_id/logs", deployController.GetDeployLogs) // 获取构建日志 - 用于外部系统查看

		{
			auth.GET("/image/", buildController.GetBuildImages) // 获取可部署镜像列表
			auth.GET("/appinfo", buildController.GetAppInfo)    // 获取CI信息

			// 构建相关API
			builds := auth.Group("/builds")
			{
				builds.POST("", apiController.CreateBuild)             // 创建构建
				builds.GET("", apiController.ListBuilds)               // 列出构建
				builds.GET("/:id", apiController.GetBuild)             // 获取构建详情
				builds.POST("/:app_info_id/ci", apiController.BuildCI) // 持续集成
				builds.POST("/cancel/:id/", apiController.CancelBuild) // 取消构建
			}

			// 部署相关API
			deploys := auth.Group("/deploys")
			{
				deploys.POST("", apiController.CreateDeploy)            // 创建部署
				deploys.GET("", apiController.ListDeploys)              // 列出部署
				deploys.GET("/:id", apiController.GetDeploy)            // 获取部署详情
				deploys.POST("/:id/cancel", apiController.CancelDeploy) // 取消部署
				deploys.POST("/deploy", deployController.CreateDeploy)  // 创建部署任务
			}

			// 回滚相关API
			rollback := auth.Group("/rollback")
			{
				rollback.POST("", deployController.CreateRollback)               // 创建回滚任务
				rollback.GET("/history", deployController.GetRollbackHistory)    // 获取回滚历史
				rollback.POST("/normal", deployController.CreateRollbackToImage) // 创建回滚到指定镜像任务
			}

			// 发布工单相关API
			orders := auth.Group("/orders")
			{
				orders.POST("", apiController.CreateOrder)                             // 创建工单
				orders.GET("", apiController.ListOrders)                               // 列出工单
				orders.GET("/:id", apiController.GetOrder)                             // 获取工单详情
				orders.GET("/:id/apps", apiController.GetOrderApps)                    // 获取工单应用
				orders.POST("/:id/cancel", apiController.CancelOrder)                  // 取消工单
				orders.POST("/:id/apps/:app_id/deploy", apiController.DeployApp)       // 部署应用
				orders.POST("/:id/apps/:app_id/cancel", apiController.CancelAppDeploy) // 取消应用部署
			}

			// 应用相关API
			apps := auth.Group("/apps")
			{
				apps.GET("", apiController.ListApps)                     // 列出应用
				apps.GET("/:id", apiController.GetApp)                   // 获取应用详情
				apps.GET("/:id/versions", apiController.GetAppVersions)  // 获取应用版本
				apps.POST("/:id/pipeline", apiController.CreatePipeline) // 创建流水线
			}

			// Tekton流水线相关API
			pipelines := auth.Group("/pipeline")
			{
				// pipelines.GET("/:id", apiController.GetPipeline)            // 获取流水线详情
				// pipelines.POST("/:id/run", apiController.RunPipeline)       // 运行流水线
				// pipelines.POST("/:id/cancel", apiController.CancelPipeline) // 取消流水线
				// pipelines.POST("/:id/pause", apiController.PausePipeline)   // 暂停流水线
				// pipelines.POST("/:id/resume", apiController.ResumePipeline) // 恢复流水线
				// pipelines.POST("/:id/delete", apiController.DeletePipeline) // 删除流水线
				// pipelines.POST("/:id/clone", apiController.ClonePipeline)   // 克隆流水线
				// pipelines.POST("/:id/copy", apiController.CopyPipeline)     // 复制流水线

				// Tasks
				tasks := pipelines.Group("/tasks")
				{
					tasks.GET("", pipelineController.GetTasks)                  // 获取任务列表
					tasks.GET("/:id", pipelineController.GetTask)               // 获取任务详情
					tasks.POST("/:id", pipelineController.CreateTask)           // 创建任务
					tasks.PUT("/:id", pipelineController.UpdateTask)            // 更新任务
					tasks.DELETE("/:id", pipelineController.DeleteTask)         // 删除任务
					tasks.POST("/sync", pipelineController.SyncTasksFromTekton) // 同步任务
				}
			}
		}
	}

	// ========================================
	// 显示所有已注册的路由
	// ========================================
	logger.Info("✅ 所有API路由设置完成")
	printRegisteredRoutes(r, logger)
}

// printRegisteredRoutes 打印所有已注册的路由
func printRegisteredRoutes(r *gin.Engine, logger logrus.FieldLogger) {
	routes := r.Routes()

	// 按路径排序
	sort.Slice(routes, func(i, j int) bool {
		return routes[i].Path < routes[j].Path
	})

	logger.Info("📋 已注册的API路由列表:")
	logger.Info("┌────────┬─────────────────────────────────────────────────┬──────────────────────────────────────┐")
	logger.Info("│ 方法   │ 路径                                            │ 处理器                               │")
	logger.Info("├────────┼─────────────────────────────────────────────────┼──────────────────────────────────────┤")

	// 分组统计
	var (
		v1Routes     = 0
		globalRoutes = 0
	)

	for _, route := range routes {
		method := padString(route.Method, 6)
		path := padString(route.Path, 47)
		handler := getHandlerName(route.Handler)
		handler = padString(handler, 36)

		logger.Infof("│ %s │ %s │ %s │", method, path, handler)

		// 统计路由分组
		if strings.HasPrefix(route.Path, "/api/v1/cicd") {
			v1Routes++
		} else {
			globalRoutes++
		}
	}

	logger.Info("└────────┴─────────────────────────────────────────────────┴──────────────────────────────────────┘")
	logger.Infof("📊 路由统计: 总计 %d 个路由", len(routes))
	logger.Infof("   ├─ API v1 路由 (/api/v1/cicd/*): %d 个", v1Routes)
	logger.Infof("   └─ 全局路由: %d 个", globalRoutes)
	logger.Info("🚀 路由系统准备就绪")
}

// padString 填充字符串到指定长度
func padString(s string, length int) string {
	// 计算中文字符的实际显示长度
	runes := []rune(s)
	displayLen := 0
	for _, r := range runes {
		if r > 127 {
			displayLen += 2 // 中文字符占用2个显示位
		} else {
			displayLen += 1 // 英文字符占用1个显示位
		}
	}

	if displayLen >= length {
		if len(runes) > length-3 {
			return string(runes[:length-3]) + "..."
		}
		return s
	}

	padding := length - displayLen
	return s + strings.Repeat(" ", padding)
}

// getHandlerName 获取处理器函数名称
func getHandlerName(handler string) string {
	// 提取函数名称的最后部分
	parts := strings.Split(handler, "/")
	if len(parts) > 0 {
		lastPart := parts[len(parts)-1]
		// 去掉包名，只保留函数名
		if dotIndex := strings.LastIndex(lastPart, "."); dotIndex >= 0 {
			return lastPart[dotIndex+1:]
		}
		return lastPart
	}
	return "Unknown"
}
